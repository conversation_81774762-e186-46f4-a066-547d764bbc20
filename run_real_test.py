#!/usr/bin/env python3
"""
Real-life test run script for ICU Lipreading MVP
Implements the 5-step test plan with frozen encoder + trainable head
"""

import os
import sys
import subprocess
import csv
import collections
from pathlib import Path

def step1_activate_and_check_data():
    """Step 1: Activate environment and confirm data"""
    print("=== Step 1: Checking data ===")
    
    # Check if manifest exists
    manifest_path = "data/manifest.csv"
    if not Path(manifest_path).exists():
        print(f"❌ Manifest file not found: {manifest_path}")
        return False
    
    # Count phrases
    phrase_counts = collections.Counter()
    try:
        with open(manifest_path) as f:
            for row in csv.DictReader(f):
                phrase = row['phrase'].strip()
                phrase_counts[phrase] += 1
        
        print("Phrase counts:")
        for phrase, count in phrase_counts.items():
            print(f"  {phrase}: {count}")
        
        total_samples = sum(phrase_counts.values())
        print(f"Total samples: {total_samples}")
        
        if total_samples == 0:
            print("❌ No data found in manifest")
            return False
        
        print("✅ Data check passed")
        return True
        
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def step2_quick_train():
    """Step 2: Quick-train the classifier head (encoder frozen)"""
    print("\n=== Step 2: Quick-training classifier head ===")
    
    # Create output directory
    out_dir = "artifacts/vsr_fasthead_v1"
    Path(out_dir).mkdir(parents=True, exist_ok=True)
    
    # Training command
    cmd = [
        sys.executable, "-m", "backend.lightweight_vsr.train",
        "--manifest", "data/manifest.csv",
        "--config", "configs/phrases26.yaml",
        "--out_dir", out_dir,
        "--epochs", "8",
        "--batch_size", "32",
        "--frames", "16",
        "--size", "96",
        "--min_per_phrase", "8",
        "--max_per_phrase", "40",
        "--freeze_encoder"
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Training completed successfully")
        print("Training output:")
        print(result.stdout[-1000:])  # Last 1000 chars
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Training failed: {e}")
        print("Error output:")
        print(e.stderr[-1000:])  # Last 1000 chars
        return False

def step3_smoke_test():
    """Step 3: Smoke-test the trained weights"""
    print("\n=== Step 3: Smoke-testing trained weights ===")
    
    # Check if weights exist
    weights_dir = Path("artifacts/vsr_fasthead_v1")
    best_pt = weights_dir / "best.pt"
    
    if not best_pt.exists():
        print(f"❌ Trained weights not found: {best_pt}")
        return False
    
    # Find a test video
    test_video = None
    test_dirs = ["test_webm_videos", "data", "videos"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            for ext in ["*.mp4", "*.webm", "*.avi"]:
                videos = list(Path(test_dir).glob(ext))
                if videos:
                    test_video = str(videos[0])
                    break
            if test_video:
                break
    
    if not test_video:
        print("❌ No test video found")
        return False
    
    print(f"Testing with video: {test_video}")
    
    # Test prediction
    test_code = f"""
from backend.lightweight_vsr.infer import predict_phrase

# First try with default threshold
result = predict_phrase('{test_video}', art_dir='artifacts/vsr_fasthead_v1')
print("Prediction result (default threshold):")
print(f"Phrase: {{result['phrase']}}")
print(f"Confidence: {{result['confidence']:.3f}}")
print(f"Top predictions: {{result.get('topk', [])}}")

# If result is "uncertain", try with lower threshold
if result['phrase'] == 'uncertain':
    print("\\nTrying with lower confidence threshold (0.55)...")
    result_low = predict_phrase('{test_video}', art_dir='artifacts/vsr_fasthead_v1', confidence_threshold=0.55)
    print(f"Phrase: {{result_low['phrase']}}")
    print(f"Confidence: {{result_low['confidence']:.3f}}")
"""
    
    try:
        result = subprocess.run([sys.executable, "-c", test_code], 
                              capture_output=True, text=True, check=True)
        print("✅ Smoke test passed")
        print("Output:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Smoke test failed: {e}")
        print("Error:")
        print(e.stderr)
        return False

def step4_test_api():
    """Step 4: Test API endpoint"""
    print("\n=== Step 4: Testing API endpoint ===")
    print("Note: This step requires manual testing")
    print("1. Set environment variable: export VSR_IMPL=lightweight")
    print("2. Start server: uvicorn backend.api.app:app --reload")
    print("3. Test with: curl -X POST http://127.0.0.1:8000/predict_v2 -F 'file=@PATH/TO/VIDEO.mp4'")
    print("✅ API test instructions provided")
    return True

def step5_debug_frames():
    """Step 5: Debug frame extraction"""
    print("\n=== Step 5: Debug frame extraction ===")
    
    # Find a test video
    test_video = None
    test_dirs = ["test_webm_videos", "data", "videos"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            for ext in ["*.mp4", "*.webm", "*.avi"]:
                videos = list(Path(test_dir).glob(ext))
                if videos:
                    test_video = str(videos[0])
                    break
            if test_video:
                break
    
    if not test_video:
        print("❌ No test video found for frame debugging")
        return False
    
    debug_code = f"""
from backend.lightweight_vsr.dataset import read_frames_uniform
import imageio
import os
import numpy as np

video_path = '{test_video}'
print(f"Processing video: {{video_path}}")

try:
    frames = read_frames_uniform(video_path, num_frames=16, size=(96, 96))
    print(f"Extracted frames shape: {{frames.shape}}")
    
    # Create debug directory
    os.makedirs('debug_frames', exist_ok=True)
    
    # Save frames as images
    for i, frame in enumerate(frames):
        # Convert from tensor to numpy and scale to 0-255
        frame_np = frame[0].numpy()  # Remove channel dimension
        frame_uint8 = (frame_np * 255).astype('uint8')
        imageio.imwrite(f'debug_frames/f{{i:02d}}.png', frame_uint8)
    
    print(f"Saved {{len(frames)}} frames to debug_frames/")
    print("✅ Frame extraction successful")
    print("Open debug_frames/*.png to verify mouth visibility")
    
except Exception as e:
    print(f"❌ Frame extraction failed: {{e}}")
    import traceback
    traceback.print_exc()
"""
    
    try:
        result = subprocess.run([sys.executable, "-c", debug_code], 
                              capture_output=True, text=True, check=True)
        print("✅ Frame debugging completed")
        print("Output:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Frame debugging failed: {e}")
        print("Error:")
        print(e.stderr)
        return False

def main():
    """Run all test steps"""
    print("🚀 Starting real-life test run for ICU Lipreading MVP")
    print("=" * 60)
    
    steps = [
        ("Data Check", step1_activate_and_check_data),
        ("Quick Training", step2_quick_train),
        ("Smoke Test", step3_smoke_test),
        ("API Test", step4_test_api),
        ("Frame Debug", step5_debug_frames)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        try:
            success = step_func()
            results.append((step_name, success))
            if not success and step_name != "API Test":  # API test is manual
                print(f"\n❌ {step_name} failed. Stopping here.")
                break
        except Exception as e:
            print(f"\n❌ {step_name} crashed: {e}")
            results.append((step_name, False))
            break
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    for step_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {step_name}: {status}")
    
    total_passed = sum(1 for _, success in results if success)
    print(f"\nPassed: {total_passed}/{len(results)} steps")
    
    if total_passed == len(results):
        print("🎉 All tests passed! Ready for real-world testing.")
    else:
        print("⚠️  Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
