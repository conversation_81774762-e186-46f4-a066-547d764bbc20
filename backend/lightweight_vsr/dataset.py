"""
Dataset implementation for ICU video phrase classification
Supports manifest CSV and folder-based organization with speaker-wise splits
"""

import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from sklearn.model_selection import train_test_split
from sklearn.utils.class_weight import compute_class_weight
import yaml
import os

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.lightweight_vsr.utils_video import VideoProcessor, VideoAugmentation, create_video_processor, create_augmentation


class ICUVideoDataset(Dataset):
    """Dataset for ICU phrase classification from mouth videos"""
    
    def __init__(self,
                 data_paths: List[str],
                 labels: List[int],
                 phrase_to_idx: Dict[str, int],
                 config: Dict,
                 metadata: Optional[pd.DataFrame] = None,
                 augment: bool = False):
        """
        Args:
            data_paths: List of video file paths
            labels: List of label indices
            phrase_to_idx: Mapping from phrase to index
            config: Configuration dictionary
            metadata: Optional metadata DataFrame with demographics
            augment: Whether to apply data augmentation
        """
        self.data_paths = data_paths
        self.labels = labels
        self.phrase_to_idx = phrase_to_idx
        self.idx_to_phrase = {v: k for k, v in phrase_to_idx.items()}
        self.config = config
        self.metadata = metadata
        self.augment = augment
        
        # Create video processor and augmentation
        self.processor = create_video_processor(config)
        self.augmentation = create_augmentation(config) if augment else None
        
        assert len(data_paths) == len(labels), "Mismatch between paths and labels"
    
    def __len__(self) -> int:
        return len(self.data_paths)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int, Dict]:
        """
        Get a single sample
        
        Returns:
            video: Processed video tensor
            label: Label index
            metadata: Sample metadata
        """
        video_path = self.data_paths[idx]
        label = self.labels[idx]
        
        # Process video
        try:
            video = self.processor.process_video(video_path)

            # Ensure correct shape
            channels = 1 if self.config.get('grayscale', True) else 3
            expected_shape = (channels, self.config.get('frames', 32),
                            self.config.get('height', 96), self.config.get('width', 96))

            if video.shape != expected_shape:
                print(f"Warning: Video shape {video.shape} != expected {expected_shape} for {video_path}")
                # Create zero tensor with correct shape
                video = torch.zeros(*expected_shape)

            # Apply augmentation if training
            if self.augment and self.augmentation:
                video = self.augmentation(video)

        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            # Return zero tensor as fallback
            channels = 1 if self.config.get('grayscale', True) else 3
            video = torch.zeros(channels, self.config.get('frames', 32),
                              self.config.get('height', 96), self.config.get('width', 96))
        
        # Prepare metadata
        sample_metadata = {
            'video_path': video_path,
            'phrase': self.idx_to_phrase[label],
            'label': label
        }
        
        # Add demographics if available
        if self.metadata is not None:
            # Find matching row (assuming video_path is in metadata)
            matching_rows = self.metadata[self.metadata['video_path'] == video_path]
            if not matching_rows.empty:
                row = matching_rows.iloc[0]
                sample_metadata.update({
                    'speaker_id': row.get('speaker_id', 'unknown'),
                    'age_group': row.get('age_group', 'unknown'),
                    'gender': row.get('gender', 'unknown'),
                    'ethnicity': row.get('ethnicity', 'unknown'),
                    'lighting': row.get('lighting', 'unknown')
                })
        
        return video, label, sample_metadata


def load_manifest_data(manifest_path: str, config: Dict) -> Tuple[List[str], List[str], List[str], pd.DataFrame]:
    """
    Load data from manifest CSV
    
    Args:
        manifest_path: Path to manifest CSV file
        config: Configuration dictionary
        
    Returns:
        video_paths: List of video file paths
        phrases: List of phrase strings
        speaker_ids: List of speaker IDs
        metadata: Full metadata DataFrame
    """
    df = pd.read_csv(manifest_path)
    
    required_columns = ['video_path', 'phrase']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns in manifest: {missing_columns}")
    
    # Filter for valid phrases
    valid_phrases = set(config['phrases'])
    df = df[df['phrase'].isin(valid_phrases)].copy()
    
    if df.empty:
        raise ValueError("No valid phrases found in manifest")
    
    # Check if video files exist
    existing_mask = df['video_path'].apply(lambda x: Path(x).exists())
    missing_count = (~existing_mask).sum()
    if missing_count > 0:
        print(f"Warning: {missing_count} video files not found, skipping...")
        df = df[existing_mask].copy()
    
    if df.empty:
        raise ValueError("No existing video files found")
    
    return (
        df['video_path'].tolist(),
        df['phrase'].tolist(),
        df['speaker_id'].tolist() if 'speaker_id' in df.columns else ['unknown'] * len(df),
        df
    )


def load_folder_data(data_dir: str, config: Dict) -> Tuple[List[str], List[str], List[str], pd.DataFrame]:
    """
    Load data from folder structure: data_dir/phrase_name/*.mp4
    
    Args:
        data_dir: Root data directory
        config: Configuration dictionary
        
    Returns:
        video_paths: List of video file paths  
        phrases: List of phrase strings
        speaker_ids: List of speaker IDs (extracted from filenames)
        metadata: Metadata DataFrame
    """
    data_path = Path(data_dir)
    video_paths = []
    phrases = []
    speaker_ids = []
    
    valid_phrases = config['phrases']
    
    for phrase in valid_phrases:
        # Convert phrase to folder name (replace spaces with underscores)
        folder_name = phrase.replace(' ', '_')
        phrase_dir = data_path / folder_name
        
        if not phrase_dir.exists():
            print(f"Warning: Phrase directory not found: {phrase_dir}")
            continue
            
        # Find video files
        video_extensions = ['.mp4', '.webm', '.avi', '.mov']
        for ext in video_extensions:
            for video_file in phrase_dir.glob(f'*{ext}'):
                video_paths.append(str(video_file))
                phrases.append(phrase)
                
                # Extract speaker ID from filename (assume format: speakerID_*.ext)
                filename = video_file.stem
                speaker_id = filename.split('_')[0] if '_' in filename else 'unknown'
                speaker_ids.append(speaker_id)
    
    if not video_paths:
        raise ValueError(f"No video files found in {data_dir}")
    
    # Create metadata DataFrame
    metadata = pd.DataFrame({
        'video_path': video_paths,
        'phrase': phrases,
        'speaker_id': speaker_ids,
        'age_group': 'unknown',
        'gender': 'unknown', 
        'ethnicity': 'unknown',
        'lighting': 'unknown'
    })
    
    return video_paths, phrases, speaker_ids, metadata


def create_phrase_mapping(phrases: List[str]) -> Dict[str, int]:
    """Create mapping from phrase to index"""
    unique_phrases = sorted(list(set(phrases)))
    return {phrase: idx for idx, phrase in enumerate(unique_phrases)}


def speaker_wise_split(video_paths: List[str], 
                      phrases: List[str],
                      speaker_ids: List[str],
                      metadata: pd.DataFrame,
                      val_split: float = 0.1,
                      test_split: float = 0.1,
                      random_state: int = 42) -> Tuple[Dict, Dict, Dict]:
    """
    Split data by speakers to prevent leakage
    
    Returns:
        train_data: Dictionary with paths, phrases, speaker_ids, metadata
        val_data: Dictionary with paths, phrases, speaker_ids, metadata  
        test_data: Dictionary with paths, phrases, speaker_ids, metadata
    """
    # Get unique speakers
    unique_speakers = list(set(speaker_ids))

    # If only one speaker, use random split instead
    if len(unique_speakers) <= 1:
        print(f"Warning: Only {len(unique_speakers)} unique speaker(s) found. Using random split instead of speaker-wise split.")
        # Create indices for splitting
        indices = list(range(len(video_paths)))

        # First split: train vs (val + test)
        train_indices, temp_indices = train_test_split(
            indices, test_size=(val_split + test_split), random_state=random_state
        )

        # Second split: val vs test
        val_indices, test_indices = train_test_split(
            temp_indices, test_size=test_split/(val_split + test_split), random_state=random_state
        )

        def create_subset(indices):
            return {
                'video_paths': [video_paths[i] for i in indices],
                'phrases': [phrases[i] for i in indices],
                'speaker_ids': [speaker_ids[i] for i in indices],
                'metadata': metadata.iloc[indices].copy()
            }

        return (
            create_subset(train_indices),
            create_subset(val_indices),
            create_subset(test_indices)
        )

    # Split speakers
    train_speakers, temp_speakers = train_test_split(
        unique_speakers, test_size=(val_split + test_split), random_state=random_state
    )
    
    val_speakers, test_speakers = train_test_split(
        temp_speakers, test_size=test_split/(val_split + test_split), random_state=random_state
    )
    
    def filter_by_speakers(target_speakers):
        mask = [spk in target_speakers for spk in speaker_ids]
        return {
            'video_paths': [p for i, p in enumerate(video_paths) if mask[i]],
            'phrases': [p for i, p in enumerate(phrases) if mask[i]],
            'speaker_ids': [s for i, s in enumerate(speaker_ids) if mask[i]],
            'metadata': metadata[metadata['speaker_id'].isin(target_speakers)].copy()
        }
    
    train_data = filter_by_speakers(train_speakers)
    val_data = filter_by_speakers(val_speakers)
    test_data = filter_by_speakers(test_speakers)
    
    print(f"Speaker split: {len(train_speakers)} train, {len(val_speakers)} val, {len(test_speakers)} test")
    print(f"Sample split: {len(train_data['video_paths'])} train, {len(val_data['video_paths'])} val, {len(test_data['video_paths'])} test")
    
    return train_data, val_data, test_data


def compute_class_weights_from_labels(labels: List[int], num_classes: int) -> torch.Tensor:
    """Compute class weights for imbalanced dataset"""
    # Get unique classes in the training data
    unique_classes = np.unique(labels)

    if len(unique_classes) == num_classes:
        # All classes present, use sklearn
        class_weights = compute_class_weight(
            'balanced',
            classes=np.arange(num_classes),
            y=labels
        )
    else:
        # Some classes missing, compute manually
        class_weights = np.ones(num_classes)
        label_counts = np.bincount(labels, minlength=num_classes)

        # Avoid division by zero
        label_counts = np.maximum(label_counts, 1)

        # Compute balanced weights for present classes
        total_samples = len(labels)
        for i in range(num_classes):
            if label_counts[i] > 0:
                class_weights[i] = total_samples / (num_classes * label_counts[i])
            else:
                class_weights[i] = 1.0  # Default weight for missing classes

    return torch.FloatTensor(class_weights)


def create_dataloaders(config: Dict,
                      manifest_path: Optional[str] = None,
                      data_dir: Optional[str] = None) -> Tuple[DataLoader, DataLoader, DataLoader, Dict]:
    """
    Create train, validation, and test dataloaders
    
    Args:
        config: Configuration dictionary
        manifest_path: Path to manifest CSV (preferred)
        data_dir: Path to data directory (fallback)
        
    Returns:
        train_loader: Training DataLoader
        val_loader: Validation DataLoader  
        test_loader: Test DataLoader
        info: Dictionary with phrase mapping and class weights
    """
    # Load data
    if manifest_path and Path(manifest_path).exists():
        video_paths, phrases, speaker_ids, metadata = load_manifest_data(manifest_path, config)
    elif data_dir and Path(data_dir).exists():
        video_paths, phrases, speaker_ids, metadata = load_folder_data(data_dir, config)
    else:
        raise ValueError("Either manifest_path or data_dir must be provided and exist")
    
    # Create phrase mapping
    phrase_to_idx = create_phrase_mapping(config['phrases'])  # Use config phrases for consistent ordering
    labels = [phrase_to_idx[phrase] for phrase in phrases]
    
    # Speaker-wise split
    train_data, val_data, test_data = speaker_wise_split(
        video_paths, phrases, speaker_ids, metadata,
        val_split=config.get('val_split', 0.1),
        test_split=config.get('test_split', 0.1)
    )
    
    # Create datasets
    train_labels = [phrase_to_idx[phrase] for phrase in train_data['phrases']]
    val_labels = [phrase_to_idx[phrase] for phrase in val_data['phrases']]
    test_labels = [phrase_to_idx[phrase] for phrase in test_data['phrases']]
    
    train_dataset = ICUVideoDataset(
        train_data['video_paths'], train_labels, phrase_to_idx, config,
        train_data['metadata'], augment=config.get('augmentation', {}).get('enabled', True)
    )
    
    val_dataset = ICUVideoDataset(
        val_data['video_paths'], val_labels, phrase_to_idx, config,
        val_data['metadata'], augment=False
    )
    
    test_dataset = ICUVideoDataset(
        test_data['video_paths'], test_labels, phrase_to_idx, config,
        test_data['metadata'], augment=False
    )
    
    # Create dataloaders
    batch_size = config.get('batch_size', 16)
    num_workers = config.get('num_workers', 4)
    
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        num_workers=num_workers, pin_memory=False, drop_last=True
    )

    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=num_workers, pin_memory=False
    )

    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=num_workers, pin_memory=False
    )
    
    # Compute class weights
    class_weights = compute_class_weights_from_labels(train_labels, len(phrase_to_idx))
    
    info = {
        'phrase_to_idx': phrase_to_idx,
        'idx_to_phrase': {v: k for k, v in phrase_to_idx.items()},
        'class_weights': class_weights,
        'num_classes': len(phrase_to_idx),
        'train_size': len(train_dataset),
        'val_size': len(val_dataset),
        'test_size': len(test_dataset)
    }
    
    return train_loader, val_loader, test_loader, info


def read_frames_uniform(video_path: Union[str, Path], num_frames: int = 16, size: Tuple[int, int] = (96, 96)) -> torch.Tensor:
    """
    Read frames uniformly from a video file for debugging purposes

    Args:
        video_path: Path to video file
        num_frames: Number of frames to extract
        size: Target frame size (height, width)

    Returns:
        Tensor of shape (num_frames, 1, height, width) for grayscale frames
    """
    import cv2

    cap = cv2.VideoCapture(str(video_path))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    if total_frames <= 0:
        cap.release()
        raise ValueError(f"No frames found in video: {video_path}")

    # Calculate frame indices for uniform sampling
    if total_frames <= num_frames:
        frame_indices = list(range(total_frames))
        # Pad with last frame if needed
        while len(frame_indices) < num_frames:
            frame_indices.append(frame_indices[-1])
    else:
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]

    frames = []
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            # Resize to target size
            resized = cv2.resize(gray, size, interpolation=cv2.INTER_AREA)
            # Normalize to [0, 1]
            normalized = resized.astype(np.float32) / 255.0
            frames.append(normalized)
        else:
            # Use last frame if read fails
            if frames:
                frames.append(frames[-1])
            else:
                # Create black frame
                black_frame = np.zeros(size, dtype=np.float32)
                frames.append(black_frame)

    cap.release()

    # Convert to tensor and add channel dimension
    frames_array = np.array(frames)  # Shape: (num_frames, height, width)
    frames_array = np.expand_dims(frames_array, axis=1)  # Shape: (num_frames, 1, height, width)

    return torch.from_numpy(frames_array)
