"""
Mobile3DTiny: Lightweight 3D CNN + BiGRU for phrase classification
Commercial-safe architecture with depthwise-separable convolutions
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple


class DepthwiseSeparable3D(nn.Module):
    """Depthwise separable 3D convolution for efficiency"""
    
    def __init__(self, in_channels: int, out_channels: int, 
                 kernel_size: Tuple[int, int, int] = (3, 3, 3),
                 stride: Tuple[int, int, int] = (1, 1, 1),
                 padding: Tuple[int, int, int] = (1, 1, 1)):
        super().__init__()
        
        # Depthwise convolution
        self.depthwise = nn.Conv3d(
            in_channels, in_channels, kernel_size, stride, padding,
            groups=in_channels, bias=False
        )
        
        # Pointwise convolution
        self.pointwise = nn.Conv3d(
            in_channels, out_channels, (1, 1, 1), bias=False
        )
        
        self.bn = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.relu(x)
        return x


class Mobile3DTiny(nn.Module):
    """
    Lightweight 3D CNN + BiGRU for direct phrase classification
    
    Architecture:
    - Depthwise-separable 3D conv blocks (≤8M params)
    - Spatial Global Average Pooling
    - BiGRU(256×2) with temporal mean pooling
    - LayerNorm → Dropout(0.2) → Linear(num_classes) → Softmax
    """
    
    def __init__(self, num_classes: int = 26, hidden_dim: int = 256,
                 num_gru_layers: int = 2, dropout: float = 0.2, input_channels: int = 1):
        super().__init__()

        self.num_classes = num_classes
        self.hidden_dim = hidden_dim
        self.input_channels = input_channels

        # 3D CNN backbone with depthwise-separable convolutions
        self.conv_blocks = nn.Sequential(
            # Block 1: input_channels → 32 channels
            DepthwiseSeparable3D(input_channels, 32, (3, 7, 7), (1, 2, 2), (1, 3, 3)),
            nn.MaxPool3d((1, 2, 2)),
            
            # Block 2: 32 → 64 channels  
            DepthwiseSeparable3D(32, 64, (3, 5, 5), (1, 1, 1), (1, 2, 2)),
            nn.MaxPool3d((1, 2, 2)),
            
            # Block 3: 64 → 128 channels
            DepthwiseSeparable3D(64, 128, (3, 3, 3), (1, 1, 1), (1, 1, 1)),
            nn.MaxPool3d((1, 2, 2)),
            
            # Block 4: 128 → 256 channels
            DepthwiseSeparable3D(128, 256, (3, 3, 3), (1, 1, 1), (1, 1, 1)),
        )
        
        # Spatial Global Average Pooling
        self.spatial_gap = nn.AdaptiveAvgPool3d((None, 1, 1))
        
        # Bidirectional GRU for temporal modeling
        self.gru = nn.GRU(
            input_size=256,
            hidden_size=hidden_dim,
            num_layers=num_gru_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_gru_layers > 1 else 0
        )
        
        # Classification head
        self.layer_norm = nn.LayerNorm(hidden_dim * 2)  # *2 for bidirectional
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_dim * 2, num_classes)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, (nn.Conv3d, nn.Linear)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm3d, nn.LayerNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.GRU):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass
        
        Args:
            x: Input tensor of shape (batch_size, 1, frames, height, width)
            
        Returns:
            Logits of shape (batch_size, num_classes)
        """
        batch_size = x.size(0)
        
        # 3D CNN feature extraction
        # Input: (B, 1, T, H, W)
        x = self.conv_blocks(x)  # (B, 256, T', H', W')
        
        # Spatial Global Average Pooling
        x = self.spatial_gap(x)  # (B, 256, T', 1, 1)
        x = x.squeeze(-1).squeeze(-1)  # (B, 256, T')
        
        # Transpose for GRU: (B, T', 256)
        x = x.transpose(1, 2)
        
        # Bidirectional GRU
        gru_out, _ = self.gru(x)  # (B, T', hidden_dim*2)
        
        # Temporal mean pooling
        x = torch.mean(gru_out, dim=1)  # (B, hidden_dim*2)
        
        # Classification head
        x = self.layer_norm(x)
        x = self.dropout(x)
        logits = self.classifier(x)  # (B, num_classes)
        
        return logits
    
    def get_num_parameters(self) -> int:
        """Get total number of trainable parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)


def create_model(num_classes: int = 26, **kwargs) -> Mobile3DTiny:
    """Factory function to create Mobile3DTiny model"""
    return Mobile3DTiny(num_classes=num_classes, **kwargs)


if __name__ == "__main__":
    # Test model
    model = Mobile3DTiny(num_classes=26)
    print(f"Model parameters: {model.get_num_parameters():,}")
    
    # Test forward pass
    x = torch.randn(2, 1, 32, 96, 96)  # (batch, channels, frames, height, width)
    logits = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {logits.shape}")
    print(f"Output probabilities sum: {F.softmax(logits, dim=1).sum(dim=1)}")
