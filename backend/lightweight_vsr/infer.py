"""
Inference system for lightweight VSR
Supports TorchScript model loading and video prediction
"""

import torch
import torch.nn.functional as F
import yaml
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import time
import json

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.model import Mobile3DTiny


class VSRInference:
    """Lightweight VSR inference engine"""
    
    def __init__(self, 
                 model_path: str,
                 config_path: str,
                 device: Optional[str] = None):
        """
        Initialize inference engine
        
        Args:
            model_path: Path to TorchScript model (.ts) or checkpoint (.ckpt)
            config_path: Path to configuration YAML file
            device: Device to run inference on ('cpu', 'cuda', or None for auto)
        """
        self.config = self.load_config(config_path)
        self.device = self._setup_device(device)
        
        # Load model
        self.model = self._load_model(model_path)
        self.model.eval()
        
        # Create video processor
        self.processor = VideoProcessor(
            target_frames=self.config.get('frames', 32),
            target_size=(self.config.get('height', 96), self.config.get('width', 96)),
            grayscale=self.config.get('grayscale', True)
        )
        
        # Get phrase mapping
        self.phrases = self.config['phrases']
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        
        print(f"VSR Inference initialized with {len(self.phrases)} phrases on {self.device}")
    
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    
    def _setup_device(self, device: Optional[str]) -> torch.device:
        """Setup computation device"""
        if device is None:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        return torch.device(device)
    
    def _load_model(self, model_path: str) -> torch.nn.Module:
        """Load model from TorchScript or checkpoint"""
        model_path = Path(model_path)
        
        if model_path.suffix == '.ts':
            # Load TorchScript model
            try:
                model = torch.jit.load(model_path, map_location=self.device)
                print(f"Loaded TorchScript model from {model_path}")
                return model
            except Exception as e:
                print(f"Failed to load TorchScript model: {e}")
                raise
        
        elif model_path.suffix in ['.ckpt', '.pt']:
            # Load from checkpoint
            try:
                checkpoint = torch.load(model_path, map_location=self.device)

                # Create model
                model = Mobile3DTiny(
                    num_classes=len(self.phrases),
                    hidden_dim=self.config.get('model', {}).get('hidden_dim', 256),
                    num_gru_layers=self.config.get('model', {}).get('num_gru_layers', 2),
                    dropout=self.config.get('model', {}).get('dropout', 0.2)
                )

                # Load state dict
                model.load_state_dict(checkpoint['model_state_dict'])
                model = model.to(self.device)

                print(f"Loaded checkpoint model from {model_path}")
                return model
            except Exception as e:
                print(f"Failed to load checkpoint: {e}")
                raise
        
        else:
            raise ValueError(f"Unsupported model format: {model_path.suffix}")
    
    def preprocess_video(self, video_path: Union[str, Path]) -> torch.Tensor:
        """Preprocess video for inference"""
        try:
            # Process video
            video_tensor = self.processor.process_video(video_path)
            
            # Add batch dimension
            video_tensor = video_tensor.unsqueeze(0)  # (1, C, T, H, W)
            
            return video_tensor.to(self.device)
            
        except Exception as e:
            print(f"Error preprocessing video {video_path}: {e}")
            # Return zero tensor as fallback
            channels = 1 if self.config.get('grayscale', True) else 3
            return torch.zeros(
                1, channels, 
                self.config.get('frames', 32),
                self.config.get('height', 96), 
                self.config.get('width', 96),
                device=self.device
            )
    
    def predict_raw(self, video_tensor: torch.Tensor) -> torch.Tensor:
        """Raw model prediction"""
        with torch.no_grad():
            logits = self.model(video_tensor)
            probabilities = F.softmax(logits, dim=1)
        return probabilities
    
    def predict_video(self, video_path: Union[str, Path]) -> Dict:
        """
        Predict phrase from video file
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with prediction results
        """
        start_time = time.time()
        
        # Preprocess video
        video_tensor = self.preprocess_video(video_path)
        
        # Get predictions
        probabilities = self.predict_raw(video_tensor)
        probs = probabilities.cpu().numpy()[0]  # Remove batch dimension
        
        # Get top predictions
        top_indices = np.argsort(probs)[::-1]
        top_k = []
        
        for i in range(min(5, len(self.phrases))):  # Top 5
            idx = top_indices[i]
            phrase = self.phrases[idx]
            confidence = float(probs[idx])
            top_k.append([phrase, confidence])
        
        # Best prediction
        best_idx = top_indices[0]
        best_phrase = self.phrases[best_idx]
        best_confidence = float(probs[best_idx])
        
        # Apply confidence threshold
        if best_confidence < self.confidence_threshold:
            best_phrase = "uncertain"
            best_confidence = 0.0
        
        inference_time = time.time() - start_time
        
        return {
            'phrase': best_phrase,
            'confidence': best_confidence,
            'topk': top_k,
            'inference_time_ms': inference_time * 1000,
            'video_path': str(video_path)
        }
    
    def batch_predict(self, video_paths: List[Union[str, Path]]) -> List[Dict]:
        """Predict on multiple videos"""
        results = []
        for video_path in video_paths:
            result = self.predict_video(video_path)
            results.append(result)
        return results


# Global inference engine (singleton pattern)
_inference_engine = None


def load_inference_engine(model_path: str = "artifacts/vsr_26p_v1/model.ts",
                         config_path: str = "configs/phrases26.yaml",
                         device: Optional[str] = None) -> VSRInference:
    """Load or get existing inference engine"""
    global _inference_engine
    
    if _inference_engine is None:
        _inference_engine = VSRInference(model_path, config_path, device)
    
    return _inference_engine


def predict_phrase(video_path: Union[str, Path],
                  model_path: str = None,
                  config_path: str = None,
                  art_dir: str = None,
                  confidence_threshold: float = None) -> Dict:
    """
    Convenience function for single video prediction

    Args:
        video_path: Path to video file
        model_path: Path to model file (optional if art_dir provided)
        config_path: Path to config file (optional if art_dir provided)
        art_dir: Artifact directory containing best.pt and label_map.json
        confidence_threshold: Override confidence threshold (e.g., 0.55 for lower threshold)

    Returns:
        Prediction dictionary
    """
    # If art_dir is provided, construct paths from it
    if art_dir:
        art_path = Path(art_dir)
        if not model_path:
            # Look for best.pt first, then model.ts
            if (art_path / "best.pt").exists():
                model_path = str(art_path / "best.pt")
            elif (art_path / "model.ts").exists():
                model_path = str(art_path / "model.ts")
            else:
                raise FileNotFoundError(f"No model found in {art_dir}")

        if not config_path:
            config_path = "configs/phrases26.yaml"  # Default config

    # Fallback to defaults if nothing provided
    if not model_path:
        model_path = "artifacts/vsr_26p_v1/model.ts"
    if not config_path:
        config_path = "configs/phrases26.yaml"

    engine = load_inference_engine(model_path, config_path)

    # Override confidence threshold if provided
    if confidence_threshold is not None:
        original_threshold = engine.confidence_threshold
        engine.confidence_threshold = confidence_threshold
        try:
            result = engine.predict_video(video_path)
        finally:
            # Restore original threshold
            engine.confidence_threshold = original_threshold
        return result
    else:
        return engine.predict_video(video_path)


def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def export_torchscript(checkpoint_path: str,
                      config_path: str,
                      output_path: str):
    """
    Export trained model to TorchScript
    
    Args:
        checkpoint_path: Path to trained checkpoint
        config_path: Path to configuration file
        output_path: Path to save TorchScript model
    """
    config = load_config(config_path)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Create model
    model = Mobile3DTiny(
        num_classes=len(config['phrases']),
        hidden_dim=config.get('model', {}).get('hidden_dim', 256),
        num_gru_layers=config.get('model', {}).get('num_gru_layers', 2),
        dropout=config.get('model', {}).get('dropout', 0.2)
    )
    
    # Load weights
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Create example input
    channels = 1 if config.get('grayscale', True) else 3
    example_input = torch.randn(
        1, channels,
        config.get('frames', 32),
        config.get('height', 96),
        config.get('width', 96)
    )
    
    # Trace model
    try:
        traced_model = torch.jit.trace(model, example_input)
        
        # Save TorchScript model
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        traced_model.save(output_path)
        
        print(f"TorchScript model saved to {output_path}")
        
        # Test the exported model
        test_output = traced_model(example_input)
        print(f"Export test - output shape: {test_output.shape}")
        
        return str(output_path)
        
    except Exception as e:
        print(f"Failed to export TorchScript model: {e}")
        raise


def benchmark_inference(model_path: str = "artifacts/vsr_26p_v1/model.ts",
                       config_path: str = "configs/phrases26.yaml",
                       test_videos: Optional[List[str]] = None,
                       num_runs: int = 10) -> Dict:
    """
    Benchmark inference performance
    
    Returns:
        Performance statistics
    """
    engine = load_inference_engine(model_path, config_path)
    
    if test_videos is None:
        # Create dummy video tensor for benchmarking
        channels = 1 if engine.config.get('grayscale', True) else 3
        dummy_video = torch.randn(
            1, channels,
            engine.config.get('frames', 32),
            engine.config.get('height', 96),
            engine.config.get('width', 96),
            device=engine.device
        )
        
        # Warmup
        for _ in range(5):
            _ = engine.predict_raw(dummy_video)
        
        # Benchmark
        times = []
        for _ in range(num_runs):
            start_time = time.time()
            _ = engine.predict_raw(dummy_video)
            times.append((time.time() - start_time) * 1000)  # Convert to ms
        
        return {
            'mean_time_ms': np.mean(times),
            'std_time_ms': np.std(times),
            'min_time_ms': np.min(times),
            'max_time_ms': np.max(times),
            'num_runs': num_runs,
            'device': str(engine.device)
        }
    
    else:
        # Benchmark on real videos
        times = []
        for video_path in test_videos[:num_runs]:
            start_time = time.time()
            _ = engine.predict_video(video_path)
            times.append((time.time() - start_time) * 1000)
        
        return {
            'mean_time_ms': np.mean(times),
            'std_time_ms': np.std(times),
            'min_time_ms': np.min(times),
            'max_time_ms': np.max(times),
            'num_videos': len(times),
            'device': str(engine.device)
        }


if __name__ == "__main__":
    # Test inference
    import sys
    
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        try:
            result = predict_phrase(video_path)
            print(f"Prediction: {result['phrase']} (confidence: {result['confidence']:.3f})")
            print(f"Inference time: {result['inference_time_ms']:.1f}ms")
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Usage: python infer.py <video_path>")
        
        # Run benchmark if no video provided
        try:
            stats = benchmark_inference()
            print(f"Benchmark results: {stats['mean_time_ms']:.1f}±{stats['std_time_ms']:.1f}ms")
        except Exception as e:
            print(f"Benchmark failed: {e}")
