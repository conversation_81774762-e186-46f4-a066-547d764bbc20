# backend/tts/engine.py
import os
from typing import Protocol

class TTSEngine(Protocol):
    def synthesize_wav(self, text: str, out_wav_path: str) -> None: ...

def get_tts_engine() -> TTSEngine:
    engine = os.getenv("TTS_ENGINE", "coqui").lower()
    if engine == "piper":
        from .piper_engine import PiperEngine
        return PiperEngine()
    else:
        from .coqui_engine import CoquiEngine
        return CoquiEngine()
