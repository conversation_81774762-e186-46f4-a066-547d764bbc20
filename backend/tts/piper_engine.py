# backend/tts/piper_engine.py
import os, subprocess, tempfile, shutil

# Expect PIPER_PATH to point to the 'piper' binary and PIPER_VOICE to a .onnx voice file
# Example env:
#   PIPER_PATH=/usr/local/bin/piper
#   PIPER_VOICE=/models/en_US-amy-medium.onnx
class PiperEngine:
    def __init__(self):
        self.bin = os.getenv("PIPER_PATH", "piper")
        self.voice = os.getenv("PIPER_VOICE")
        if not self.voice:
            raise RuntimeError("PiperEngine requires PIPER_VOICE env var pointing to a voice .onnx")

    def synthesize_wav(self, text: str, out_wav_path: str) -> None:
        cmd = [self.bin, "-m", self.voice, "-f", out_wav_path, "-l", "en_US"]
        p = subprocess.Popen(cmd, stdin=subprocess.PIPE)
        p.stdin.write(text.encode("utf-8"))
        p.stdin.close()
        p.wait()
        if p.returncode != 0:
            raise RuntimeError("Piper synthesis failed")
