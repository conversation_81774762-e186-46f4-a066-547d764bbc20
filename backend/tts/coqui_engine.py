# backend/tts/coqui_engine.py
import subprocess
import tempfile
import os

class CoquiEngine:
    def __init__(self):
        self.use_espeak = True
        self.tts = None

        try:
            # Try to import and initialize Coqui TTS
            from TTS.api import TTS
            print("Attempting to load Coqui TTS model...")
            # Use a simple, fast model that should work on CPU
            self.tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC")
            self.use_espeak = False
            print("✅ Coqui TTS loaded successfully")
        except Exception as e:
            print(f"⚠️  Coqui TTS failed to load ({e}), falling back to espeak")
            # Check if espeak is available
            try:
                subprocess.run(["espeak", "--version"], capture_output=True, check=True)
                print("✅ Using espeak for text-to-speech synthesis")
            except (subprocess.CalledProcessError, FileNotFoundError):
                raise RuntimeError("Neither Coqui TTS nor espeak is available for text-to-speech")

    def synthesize_wav(self, text: str, out_wav_path: str) -> None:
        if self.use_espeak:
            # Use espeak for synthesis
            cmd = [
                "espeak",
                "-s", "150",  # speed
                "-v", "en",   # voice
                "-w", out_wav_path,  # output wav file
                text
            ]
            subprocess.run(cmd, check=True)
        else:
            # Use Coqui TTS
            self.tts.tts_to_file(text=text, file_path=out_wav_path)
