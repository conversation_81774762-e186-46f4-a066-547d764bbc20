"""
Core Word Expansion System for Hybrid ICU Lipreading
Provides contextual phrase expansion for detected core words

Architecture:
1. Core word detected with >95% accuracy
2. Generate 6-8 clinically relevant phrase options
3. Patient/nurse selects intended phrase
4. Selected phrase spoken via TTS and logged
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class UrgencyLevel(Enum):
    """Clinical urgency levels for phrase prioritization"""
    CRITICAL = "critical"
    HIGH = "high" 
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class PhraseExpansion:
    """Individual phrase expansion with clinical metadata"""
    phrase: str
    clinical_context: str
    urgency: UrgencyLevel
    common_usage: float
    category: str

@dataclass
class CoreWordResult:
    """Core word detection result with expansions"""
    core_word: str
    confidence: float
    expansions: List[PhraseExpansion]
    clinical_priority: str
    timestamp: str

class CoreWordExpansionEngine:
    """Manages phrase expansion for detected core words"""
    
    def __init__(self, expansions_path: str = "data/phrase_expansions.json"):
        """
        Initialize expansion engine
        
        Args:
            expansions_path: Path to phrase expansions JSON file
        """
        self.expansions_path = Path(expansions_path)
        self.expansions_data = {}
        self.core_words = ["pillow", "help", "glasses", "phone", "doctor"]
        
        self.load_expansions()
    
    def load_expansions(self):
        """Load phrase expansions from JSON file"""
        try:
            if not self.expansions_path.exists():
                logger.error(f"Expansions file not found: {self.expansions_path}")
                self.create_default_expansions()
                return
            
            with open(self.expansions_path, 'r') as f:
                data = json.load(f)
            
            self.expansions_data = data.get('phrase_expansions', {})
            logger.info(f"Loaded expansions for {len(self.expansions_data)} core words")
            
        except Exception as e:
            logger.error(f"Failed to load expansions: {e}")
            self.create_default_expansions()
    
    def create_default_expansions(self):
        """Create minimal default expansions if file missing"""
        logger.warning("Creating minimal default expansions")
        
        self.expansions_data = {
            "help": {
                "category": "assistance_request",
                "clinical_priority": "high",
                "expansions": [
                    {
                        "phrase": "I need help",
                        "clinical_context": "general_assistance",
                        "urgency": "high",
                        "common_usage": 0.95
                    },
                    {
                        "phrase": "Help me sit up",
                        "clinical_context": "positioning_assistance", 
                        "urgency": "medium",
                        "common_usage": 0.8
                    }
                ]
            }
        }
    
    def get_expansions(self, 
                      core_word: str, 
                      confidence: float,
                      max_expansions: int = 8,
                      min_confidence: float = 0.8) -> Optional[CoreWordResult]:
        """
        Get phrase expansions for detected core word
        
        Args:
            core_word: Detected core word
            confidence: Detection confidence score
            max_expansions: Maximum number of expansions to return
            min_confidence: Minimum confidence threshold
            
        Returns:
            CoreWordResult with expansions or None if confidence too low
        """
        # Validate confidence threshold
        if confidence < min_confidence:
            logger.warning(f"Core word '{core_word}' confidence {confidence:.3f} below threshold {min_confidence}")
            return None
        
        # Validate core word
        if core_word not in self.expansions_data:
            logger.error(f"No expansions found for core word: {core_word}")
            return None
        
        word_data = self.expansions_data[core_word]
        
        # Convert expansion data to PhraseExpansion objects
        expansions = []
        for exp_data in word_data.get('expansions', []):
            expansion = PhraseExpansion(
                phrase=exp_data['phrase'],
                clinical_context=exp_data['clinical_context'],
                urgency=UrgencyLevel(exp_data['urgency']),
                common_usage=exp_data['common_usage'],
                category=word_data['category']
            )
            expansions.append(expansion)
        
        # Sort by urgency and common usage
        expansions.sort(key=lambda x: (
            self._urgency_priority(x.urgency),
            -x.common_usage  # Negative for descending order
        ))
        
        # Limit to max_expansions
        expansions = expansions[:max_expansions]
        
        # Create result
        result = CoreWordResult(
            core_word=core_word,
            confidence=confidence,
            expansions=expansions,
            clinical_priority=word_data['clinical_priority'],
            timestamp=self._get_timestamp()
        )
        
        logger.info(f"Generated {len(expansions)} expansions for '{core_word}' (confidence: {confidence:.3f})")
        
        return result
    
    def _urgency_priority(self, urgency: UrgencyLevel) -> int:
        """Convert urgency to priority number (lower = higher priority)"""
        priority_map = {
            UrgencyLevel.CRITICAL: 0,
            UrgencyLevel.HIGH: 1,
            UrgencyLevel.MEDIUM: 2,
            UrgencyLevel.LOW: 3
        }
        return priority_map.get(urgency, 3)
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for logging"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_expansion_summary(self, core_word: str) -> Dict:
        """Get summary information for a core word"""
        if core_word not in self.expansions_data:
            return {}
        
        word_data = self.expansions_data[core_word]
        expansions = word_data.get('expansions', [])
        
        return {
            'core_word': core_word,
            'category': word_data['category'],
            'clinical_priority': word_data['clinical_priority'],
            'total_expansions': len(expansions),
            'urgency_distribution': self._get_urgency_distribution(expansions),
            'avg_common_usage': sum(exp['common_usage'] for exp in expansions) / len(expansions) if expansions else 0
        }
    
    def _get_urgency_distribution(self, expansions: List[Dict]) -> Dict[str, int]:
        """Get distribution of urgency levels in expansions"""
        distribution = {level.value: 0 for level in UrgencyLevel}
        
        for exp in expansions:
            urgency = exp.get('urgency', 'low')
            if urgency in distribution:
                distribution[urgency] += 1
        
        return distribution
    
    def validate_clinical_safety(self) -> List[str]:
        """Validate all expansions for clinical safety and appropriateness"""
        issues = []
        
        # Clinical safety keywords that should not appear
        unsafe_keywords = [
            'die', 'death', 'kill', 'suicide', 'harm', 'hurt', 'blood',
            'emergency', 'code', 'crash', 'stop breathing'
        ]
        
        for core_word, word_data in self.expansions_data.items():
            for exp in word_data.get('expansions', []):
                phrase = exp['phrase'].lower()
                
                # Check for unsafe keywords
                for keyword in unsafe_keywords:
                    if keyword in phrase:
                        issues.append(f"Unsafe keyword '{keyword}' in phrase: {exp['phrase']}")
                
                # Check phrase length (should be concise for ICU setting)
                if len(exp['phrase'].split()) > 8:
                    issues.append(f"Phrase too long (>8 words): {exp['phrase']}")
                
                # Check urgency consistency
                if exp['urgency'] == 'critical' and exp['common_usage'] > 0.5:
                    issues.append(f"Critical urgency phrase has high common usage: {exp['phrase']}")
        
        if issues:
            logger.warning(f"Found {len(issues)} clinical safety issues")
        else:
            logger.info("All expansions passed clinical safety validation")
        
        return issues
    
    def add_custom_expansion(self, 
                           core_word: str, 
                           phrase: str, 
                           clinical_context: str,
                           urgency: str = "medium",
                           common_usage: float = 0.5) -> bool:
        """
        Add custom phrase expansion for a core word
        
        Args:
            core_word: Target core word
            phrase: New phrase to add
            clinical_context: Clinical context description
            urgency: Urgency level (critical/high/medium/low)
            common_usage: Usage frequency score (0.0-1.0)
            
        Returns:
            True if added successfully, False otherwise
        """
        try:
            if core_word not in self.expansions_data:
                logger.error(f"Cannot add expansion for unknown core word: {core_word}")
                return False
            
            # Validate urgency level
            if urgency not in [level.value for level in UrgencyLevel]:
                logger.error(f"Invalid urgency level: {urgency}")
                return False
            
            # Create new expansion
            new_expansion = {
                "phrase": phrase,
                "clinical_context": clinical_context,
                "urgency": urgency,
                "common_usage": common_usage
            }
            
            # Add to expansions
            self.expansions_data[core_word]['expansions'].append(new_expansion)
            
            logger.info(f"Added custom expansion for '{core_word}': {phrase}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add custom expansion: {e}")
            return False
    
    def get_all_core_words(self) -> List[str]:
        """Get list of all supported core words"""
        return list(self.expansions_data.keys())
    
    def get_stats(self) -> Dict:
        """Get overall expansion system statistics"""
        total_expansions = sum(
            len(word_data.get('expansions', []))
            for word_data in self.expansions_data.values()
        )
        
        return {
            'total_core_words': len(self.expansions_data),
            'total_expansions': total_expansions,
            'avg_expansions_per_word': total_expansions / len(self.expansions_data) if self.expansions_data else 0,
            'supported_core_words': list(self.expansions_data.keys())
        }


# Global expansion engine instance
_expansion_engine = None

def get_expansion_engine() -> CoreWordExpansionEngine:
    """Get global expansion engine instance (singleton pattern)"""
    global _expansion_engine
    
    if _expansion_engine is None:
        _expansion_engine = CoreWordExpansionEngine()
    
    return _expansion_engine


def expand_core_word(core_word: str, confidence: float, max_expansions: int = 8) -> Optional[CoreWordResult]:
    """
    Convenience function for core word expansion
    
    Args:
        core_word: Detected core word
        confidence: Detection confidence
        max_expansions: Maximum expansions to return
        
    Returns:
        CoreWordResult with expansions or None
    """
    engine = get_expansion_engine()
    return engine.get_expansions(core_word, confidence, max_expansions)


if __name__ == "__main__":
    # Test the expansion system
    engine = CoreWordExpansionEngine()
    
    # Test each core word
    test_words = ["pillow", "help", "glasses", "phone", "doctor"]
    
    for word in test_words:
        print(f"\n=== Testing '{word}' ===")
        result = engine.get_expansions(word, confidence=0.9)
        
        if result:
            print(f"Core word: {result.core_word} (confidence: {result.confidence:.3f})")
            print(f"Clinical priority: {result.clinical_priority}")
            print("Expansions:")
            for i, exp in enumerate(result.expansions, 1):
                print(f"  {i}. {exp.phrase} [{exp.urgency.value}] ({exp.common_usage:.2f})")
        else:
            print(f"No expansions found for '{word}'")
    
    # Validate clinical safety
    print("\n=== Clinical Safety Validation ===")
    issues = engine.validate_clinical_safety()
    if issues:
        for issue in issues:
            print(f"⚠️  {issue}")
    else:
        print("✅ All expansions passed clinical safety validation")
    
    # Print statistics
    print("\n=== System Statistics ===")
    stats = engine.get_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")
