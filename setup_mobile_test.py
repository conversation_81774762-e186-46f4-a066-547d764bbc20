#!/usr/bin/env python3
"""
Setup script for mobile testing with Expo Go
Gets LAN IP and updates mobile app configuration
"""

import subprocess
import socket
import re
import sys
from pathlib import Path

def get_lan_ip():
    """Get the LAN IP address of this machine"""
    try:
        # Method 1: Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        pass
    
    try:
        # Method 2: Use ifconfig on macOS/Linux
        result = subprocess.run(['ifconfig'], capture_output=True, text=True)
        if result.returncode == 0:
            # Look for inet addresses that are not localhost
            lines = result.stdout.split('\n')
            for line in lines:
                if 'inet ' in line and '127.0.0.1' not in line:
                    match = re.search(r'inet (\d+\.\d+\.\d+\.\d+)', line)
                    if match:
                        ip = match.group(1)
                        # Prefer 192.168.x.x or 10.x.x.x addresses
                        if ip.startswith('192.168.') or ip.startswith('10.'):
                            return ip
    except Exception:
        pass
    
    try:
        # Method 3: Use hostname
        hostname = socket.gethostname()
        ip = socket.gethostbyname(hostname)
        if ip != '127.0.0.1':
            return ip
    except Exception:
        pass
    
    return None

def update_mobile_config(ip_address):
    """Update the mobile app configuration with the correct IP"""
    config_path = Path("LipreadingApp/config.ts")
    
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Read current config
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Update the API_BASE_URL
        new_url = f"http://{ip_address}:8000"
        updated_content = re.sub(
            r"API_BASE_URL: '[^']*'",
            f"API_BASE_URL: '{new_url}'",
            content
        )
        
        # Write updated config
        with open(config_path, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated mobile config to use: {new_url}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update config: {e}")
        return False

def check_backend_server(ip_address):
    """Check if the backend server is running"""
    try:
        import requests
        response = requests.get(f"http://{ip_address}:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running and accessible")
            return True
        else:
            print(f"⚠️  Backend server responded with status: {response.status_code}")
            return False
    except ImportError:
        print("⚠️  requests module not available, skipping server check")
        return True
    except Exception as e:
        print(f"❌ Backend server not accessible: {e}")
        return False

def print_expo_instructions():
    """Print instructions for running the app on Expo Go"""
    print("\n" + "="*60)
    print("📱 EXPO GO SETUP INSTRUCTIONS")
    print("="*60)
    print()
    print("1. Install Expo Go on your phone:")
    print("   📱 iOS: https://apps.apple.com/app/expo-go/id982107779")
    print("   🤖 Android: https://play.google.com/store/apps/details?id=host.exp.exponent")
    print()
    print("2. Make sure your phone and laptop are on the same WiFi network")
    print()
    print("3. Start the Expo development server:")
    print("   cd LipreadingApp")
    print("   npx expo start")
    print()
    print("4. Scan the QR code with:")
    print("   📱 iOS: Camera app")
    print("   🤖 Android: Expo Go app")
    print()
    print("5. The app will load on your phone and connect to the backend")
    print()
    print("🎯 TESTING STEPS:")
    print("- Point the front camera at your mouth")
    print("- Tap the record button")
    print("- Say one of the ICU phrases clearly")
    print("- Wait for the prediction result")
    print()
    print("📋 ICU PHRASES TO TRY:")
    phrases = [
        "where am i",
        "i need help", 
        "i m in pain",
        "my chest hurts",
        "i feel anxious",
        "stay with me please"
    ]
    for phrase in phrases:
        print(f"   • \"{phrase}\"")
    print()

def main():
    """Main setup function"""
    print("🚀 Setting up mobile testing with Expo Go")
    print("="*50)
    
    # Get LAN IP
    print("🔍 Finding LAN IP address...")
    ip_address = get_lan_ip()
    
    if not ip_address:
        print("❌ Could not determine LAN IP address")
        print("Please manually update LipreadingApp/config.ts with your laptop's IP")
        print("You can find it with: ifconfig | grep 'inet ' | grep -v 127.0.0.1")
        return
    
    print(f"✅ Found LAN IP: {ip_address}")
    
    # Update mobile config
    print("📝 Updating mobile app configuration...")
    if not update_mobile_config(ip_address):
        return
    
    # Check if backend is running
    print("🔍 Checking backend server...")
    server_running = check_backend_server(ip_address)
    
    if not server_running:
        print("\n⚠️  Backend server is not running. Please start it first:")
        print("   export VSR_IMPL=lightweight")
        print("   uvicorn backend.api.app:app --host 0.0.0.0 --port 8000 --reload")
        print()
    
    # Print Expo instructions
    print_expo_instructions()
    
    if server_running:
        print("🎉 Setup complete! Ready for mobile testing.")
    else:
        print("⚠️  Setup complete, but start the backend server first.")

if __name__ == "__main__":
    main()
