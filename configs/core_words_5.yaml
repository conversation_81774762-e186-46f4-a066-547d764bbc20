# Core Word ICU Lipreading Configuration - CLEANED
# End-to-end training without pretrained weights

# Core vocabulary (5 anchor words for clinical communication)
phrases:
  - "pillow"
  - "help"
  - "glasses"
  - "phone"
  - "doctor"

# Video processing parameters - RGB for pretrained weights
video:
  num_frames: 16
  frame_size: [112, 112]  # Match pretrained model input size
  grayscale: false        # RGB for pretrained weights
  use_mouth_detection: true
  mouth_detection_fallback: true

# Transforms and normalization - ImageNet stats for pretrained models
transforms:
  normalize:
    mean: [0.485, 0.456, 0.406]  # ImageNet RGB normalization
    std: [0.229, 0.224, 0.225]

# Model parameters - FREEZE encoder initially with pretrained weights
model:
  backbone: "mobile3d_tiny"
  input_channels: 3
  num_classes: 5
  hidden_dim: 256
  num_gru_layers: 2
  dropout: 0.4
  freeze_encoder: true    # Start with frozen pretrained encoder
  freeze_bn: true         # Keep BatchNorm stats stable

# Training parameters - Head-only warmup then fine-tuning
training:
  batch_size: 32
  learning_rate: 0.001   # Higher LR for head-only training
  weight_decay: 0.01
  epochs: 12             # Head-only warmup phase
  gradient_clip_norm: 1.0
  early_stopping_patience: 8
  min_delta: 0.005

# Fine-tuning parameters - Unfreeze last blocks
finetune:
  unfreeze_at_epoch: 6   # After 6 epochs, unfreeze last block
  which_blocks: ["stage4"]  # Last stage of mobile3d
  lr_encoder: 0.0001     # Lower LR for encoder fine-tuning
  lr_head: 0.0005        # Moderate LR for head

# Optimizer
optimizer:
  type: "adamw"
  lr: 0.0002
  weight_decay: 0.0001
  betas: [0.9, 0.999]

# Learning rate scheduler
scheduler:
  type: "cosine_annealing_warm_restarts"
  T_0: 10
  T_mult: 2
  eta_min: 0.00001

# Loss function
loss:
  type: "cross_entropy"
  label_smoothing: 0.1

# Dataset configuration - USE PREDEFINED SPLITS
dataset:
  use_predefined_splits: true
  respect_manifest_split: true  # Honor the 'split' column in manifest
  train_manifest: "data/core_words/train_manifest.csv"
  val_manifest: "data/core_words/val_manifest.csv"
  test_manifest: "data/core_words/test_manifest.csv"
  speaker_column: "speaker"
  num_workers: 0
  pin_memory: false

# Data augmentation
augmentation:
  brightness_contrast_range: 0.15
  scale_range: 0.1
  vertical_jitter: 6
  temporal_jitter: 3
  rotation_range: 8
  horizontal_flip: 0.5

# Mixed precision training - DISABLED for CPU
amp: false

# Evaluation metrics
metrics:
  primary: "accuracy"
  track: ["accuracy", "precision", "recall", "f1_macro", "f1_per_class"]

# Clinical validation requirements
clinical:
  target_accuracy: 0.95
  min_per_class_accuracy: 0.90
  max_confusion_rate: 0.05

# Output paths
paths:
  output_dir: "artifacts/core_words_v2"
  checkpoint_dir: "artifacts/core_words_v2/checkpoints"
  logs_dir: "artifacts/core_words_v2/logs"
