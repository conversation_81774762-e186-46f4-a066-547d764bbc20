# Core Word ICU Lipreading Configuration
# Hybrid system: 5 anchor words for >95% accuracy + predictive expansion

# Core vocabulary (5 anchor words for clinical communication)
phrases:
  - "pillow"
  - "help" 
  - "glasses"
  - "phone"
  - "doctor"

# Video processing parameters (optimized for core words)
video:
  num_frames: 16        # Reduced from 32 for faster processing
  frame_size: [96, 96]  # Standard resolution
  grayscale: true
  use_mouth_detection: true
  mouth_detection_fallback: true

# Model parameters - UNFREEZE for end-to-end training
model:
  backbone: "mobile3d_tiny"
  hidden_dim: 256       # Increase capacity for full model training
  num_gru_layers: 2
  dropout: 0.4          # Increase regularization
  freeze_encoder: false # CRITICAL: Must unfreeze for learning without pretrained weights

# Training parameters - Optimize for end-to-end learning
training:
  batch_size: 16        # Reduce for stability with full model training
  learning_rate: 0.0002 # Lower LR for full model training
  weight_decay: 0.02    # Increase regularization
  epochs: 50            # More epochs needed for end-to-end training
  gradient_clip_norm: 1.0 # Add gradient clipping
  early_stopping_patience: 8
  min_delta: 0.005

# Learning rate scheduler - Add proper scheduling
scheduler:
  type: "cosine_annealing_warm_restarts"
  T_0: 10                     # Restart every 10 epochs
  T_mult: 2                   # Double restart period
  eta_min: 0.00001           # Minimum LR

# Loss function - Add label smoothing
loss:
  type: "cross_entropy"
  label_smoothing: 0.1

# Data parameters
dataset:
  manifest_path: "data/core_words/manifest.csv"
  train_manifest: "data/core_words/train_manifest.csv"
  val_manifest: "data/core_words/val_manifest.csv"
  test_manifest: "data/core_words/test_manifest.csv"

  # Data splits (speaker-wise to prevent leakage)
  val_split: 0.2
  test_split: 0.1

  # Data filtering
  min_per_phrase: 10    # Minimum samples per core word
  max_per_phrase: 200   # Maximum samples per core word

  # Data loader settings (fix multiprocessing issues)
  num_workers: 0        # Disable multiprocessing to avoid pickle errors
  pin_memory: false     # Disable for CPU training

# Loss function (class-weighted for imbalanced data)
loss:
  type: "weighted_cross_entropy"
  class_weights: "auto"  # Automatically computed from data
  label_smoothing: 0.1   # Helps with generalization

# Optimizer
optimizer:
  type: "adamw"
  lr: 0.001
  weight_decay: 0.01
  betas: [0.9, 0.999]

# Learning rate scheduler - Override with warm restarts
scheduler:
  type: "cosine_annealing_warm_restarts"
  T_0: 10                     # Restart every 10 epochs
  T_mult: 2                   # Double restart period
  eta_min: 0.00001           # Minimum LR

# Data augmentation - Increase for better generalization
augmentation:
  brightness_contrast_range: 0.15  # ±15% for better robustness
  scale_range: 0.1                 # ±10% scaling
  vertical_jitter: 6               # ±6 pixels
  temporal_jitter: 3               # ±3 frames
  rotation_range: 8                # ±8 degrees
  horizontal_flip: 0.5             # Add horizontal flip for generalization

# Evaluation metrics
metrics:
  primary: "accuracy"
  track: ["accuracy", "precision", "recall", "f1_macro", "f1_per_class"]
  
# Confidence thresholds
confidence:
  threshold: 0.8        # High threshold for core words (vs 0.6 for phrases)
  uncertain_label: "uncertain"

# Clinical validation requirements
clinical:
  target_accuracy: 0.95           # >95% validation accuracy required
  min_per_class_accuracy: 0.90    # Each core word must achieve >90%
  max_confusion_rate: 0.05        # <5% confusion between core words

# Mixed precision training
amp: true

# Inference settings
inference:
  batch_size: 1
  device: "auto"  # auto-detect GPU/CPU
  
# Logging and checkpointing
logging:
  log_interval: 10      # Log every 10 batches
  save_best_only: true
  save_last: true
  tensorboard: true
  
# Output paths
paths:
  output_dir: "artifacts/core_words_v1"
  checkpoint_dir: "artifacts/core_words_v1/checkpoints"
  logs_dir: "artifacts/core_words_v1/logs"
  
# Validation criteria (must meet before Phase 2)
validation:
  accuracy_threshold: 0.95        # Overall accuracy >95%
  per_class_threshold: 0.90       # Each class >90%
  confusion_matrix_analysis: true
  clinical_safety_check: true
