# Core Word ICU Lipreading Configuration
# Hybrid system: 5 anchor words for >95% accuracy + predictive expansion

# Core vocabulary (5 anchor words for clinical communication)
phrases:
  - "pillow"
  - "help" 
  - "glasses"
  - "phone"
  - "doctor"

# Video processing parameters (optimized for core words)
video:
  num_frames: 16        # Reduced from 32 for faster processing
  frame_size: [96, 96]  # Standard resolution
  grayscale: true
  use_mouth_detection: true
  mouth_detection_fallback: true

# Model parameters (frozen encoder + small classification head)
model:
  backbone: "mobile3d_tiny"
  hidden_dim: 128       # Reduced for 5-class problem
  num_gru_layers: 2
  dropout: 0.3          # Increased for better generalization
  freeze_encoder: true  # Critical: freeze visual encoder, train only head

# Training parameters (optimized for core word accuracy)
training:
  batch_size: 32
  learning_rate: 0.001
  weight_decay: 0.01
  epochs: 30            # Increased for >95% accuracy target
  early_stopping_patience: 5
  min_delta: 0.001

# Data parameters
dataset:
  manifest_path: "data/core_words/manifest.csv"
  train_manifest: "data/core_words/train_manifest.csv"
  val_manifest: "data/core_words/val_manifest.csv"
  test_manifest: "data/core_words/test_manifest.csv"
  
  # Data splits (speaker-wise to prevent leakage)
  val_split: 0.2
  test_split: 0.1
  
  # Data filtering
  min_per_phrase: 10    # Minimum samples per core word
  max_per_phrase: 200   # Maximum samples per core word

# Loss function (class-weighted for imbalanced data)
loss:
  type: "weighted_cross_entropy"
  class_weights: "auto"  # Automatically computed from data
  label_smoothing: 0.1   # Helps with generalization

# Optimizer
optimizer:
  type: "adamw"
  lr: 0.001
  weight_decay: 0.01
  betas: [0.9, 0.999]

# Learning rate scheduler
scheduler:
  type: "cosine_annealing"
  warmup_epochs: 3
  min_lr: 0.0001

# Data augmentation (conservative for clinical accuracy)
augmentation:
  brightness_contrast_range: 0.1  # ±10% (reduced from 15%)
  scale_range: 0.05               # ±5% (reduced from 10%)
  vertical_jitter: 4              # ±4 pixels (reduced from 8)
  temporal_jitter: 2              # ±2 frames (reduced from 4)
  rotation_range: 5               # ±5 degrees (new, conservative)

# Evaluation metrics
metrics:
  primary: "accuracy"
  track: ["accuracy", "precision", "recall", "f1_macro", "f1_per_class"]
  
# Confidence thresholds
confidence:
  threshold: 0.8        # High threshold for core words (vs 0.6 for phrases)
  uncertain_label: "uncertain"

# Clinical validation requirements
clinical:
  target_accuracy: 0.95           # >95% validation accuracy required
  min_per_class_accuracy: 0.90    # Each core word must achieve >90%
  max_confusion_rate: 0.05        # <5% confusion between core words

# Mixed precision training
amp: true

# Inference settings
inference:
  batch_size: 1
  device: "auto"  # auto-detect GPU/CPU
  
# Logging and checkpointing
logging:
  log_interval: 10      # Log every 10 batches
  save_best_only: true
  save_last: true
  tensorboard: true
  
# Output paths
paths:
  output_dir: "artifacts/core_words_v1"
  checkpoint_dir: "artifacts/core_words_v1/checkpoints"
  logs_dir: "artifacts/core_words_v1/logs"
  
# Validation criteria (must meet before Phase 2)
validation:
  accuracy_threshold: 0.95        # Overall accuracy >95%
  per_class_threshold: 0.90       # Each class >90%
  confusion_matrix_analysis: true
  clinical_safety_check: true
