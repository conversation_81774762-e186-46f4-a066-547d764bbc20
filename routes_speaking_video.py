# routes_speaking_video.py
import os, uuid, tempfile, subprocess, math, json
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse

from backend.tts.engine import get_tts_engine

router = APIRouter()

DEFAULT_DELAY_MS = int(os.getenv("TTS_DELAY_MS", "130"))
RATE_MULTIPLIER = float(os.getenv("TTS_RATE_MULTIPLIER", "1.0"))

def _tmpdir() -> Path:
    return Path(tempfile.mkdtemp(prefix="speakvid_"))

def _run(cmd: list[str]) -> str:
    out = subprocess.check_output(cmd, stderr=subprocess.STDOUT)
    return out.decode().strip()

def ffprobe_duration(path: Path) -> float:
    return float(_run([
        "ffprobe", "-v", "error",
        "-show_entries", "format=duration",
        "-of", "default=noprint_wrappers=1:nokey=1",
        str(path)
    ]))

def maybe_to_mp4(input_path: Path) -> Path:
    # if webm or anything else, convert to mp4 (h264 + yuv420p)
    suffix = input_path.suffix.lower()
    if suffix == ".mp4":
        return input_path
    out = input_path.with_suffix(".mp4")
    _run(["ffmpeg", "-y", "-i", str(input_path),
          "-c:v", "libx264", "-pix_fmt", "yuv420p",
          "-c:a", "aac", "-b:a", "128k", str(out)])
    return out

def atempo_chain(speed: float) -> str:
    # Chain atempo filters so final product ≈ speed
    if speed <= 0: speed = 1.0
    filters = []
    # bring inside [0.5, 2.0] by chaining
    while speed < 0.5:
        filters.append("atempo=0.5")
        speed /= 0.5
    while speed > 2.0:
        filters.append("atempo=2.0")
        speed /= 2.0
    filters.append(f"atempo={speed:.4f}")
    return ",".join(filters)

def mux_with_tts(video_in: Path, tts_wav: Path, out_mp4: Path, delay_ms: int, speed_factor: float):
    a_filters = []
    if delay_ms > 0:
        a_filters.append(f"adelay={delay_ms}|{delay_ms}")
    if abs(speed_factor - 1.0) > 1e-3:
        a_filters.append(atempo_chain(speed_factor))
    a_filters.append("aresample=48000")
    a_filter = ",".join(a_filters)

    # Map video from 0, audio from filtered 1
    cmd = [
        "ffmpeg", "-y",
        "-i", str(video_in),
        "-i", str(tts_wav),
        "-filter_complex", f"[1:a]{a_filter}[vo]",
        "-map", "0:v:0", "-map", "[vo]",
        "-c:v", "copy",
        "-c:a", "aac", "-b:a", "128k",
        "-shortest",
        str(out_mp4)
    ]
    _run(cmd)

def internal_predict_phrase(video_path: Path) -> Tuple[str, float]:
    """
    Direct prediction using the loaded model to avoid circular HTTP calls.
    """
    try:
        # Import the model from the main module
        from real_model_backend import vsr_model

        if vsr_model is None:
            raise HTTPException(status_code=500, detail="VSR model not loaded")

        # Use the model's predict method directly
        result = vsr_model.predict(str(video_path))
        return result["prediction"], result["confidence"]

    except Exception as e:
        # Fallback to a default prediction if model fails
        print(f"Warning: Direct model prediction failed ({e}), using fallback")
        return "help", 0.5  # Default fallback

@router.post("/speak_video")
async def speak_video(
    file: UploadFile = File(...),
    phrase: Optional[str] = Form(None),
    delay_ms: int = Form(DEFAULT_DELAY_MS),
):
    td = _tmpdir()
    raw_in = td / f"in_{uuid.uuid4().hex}{Path(file.filename).suffix if file.filename else '.mp4'}"
    with open(raw_in, "wb") as f:
        f.write(await file.read())

    # Convert to mp4 if needed
    mp4_in = maybe_to_mp4(raw_in)

    # Get phrase if not provided
    pred_phrase = phrase
    conf = None
    if not pred_phrase:
        pred_phrase, conf = internal_predict_phrase(mp4_in)

    # TTS: open-source engine
    tts_wav = td / "tts.wav"
    engine = get_tts_engine()
    engine.synthesize_wav(pred_phrase, str(tts_wav))

    # Durations and speed match
    vlen = ffprobe_duration(mp4_in)          # seconds
    alen = ffprobe_duration(tts_wav)         # seconds
    vlen_eff = max(0.1, vlen - (delay_ms / 1000.0))
    base_speed = vlen_eff / max(0.1, alen)
    speed = base_speed * RATE_MULTIPLIER

    out_path = td / f"spoken_{uuid.uuid4().hex}.mp4"
    mux_with_tts(mp4_in, tts_wav, out_path, delay_ms, speed)

    headers = {}
    if conf is not None:
        headers["X-Pred-Confidence"] = str(conf)
    headers["X-Phrase"] = pred_phrase

    return FileResponse(str(out_path), media_type="video/mp4", filename=out_path.name, headers=headers)
