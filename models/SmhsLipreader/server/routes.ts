import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import multer from "multer";
import path from "path";
import fs from "fs";

// Set up multer for handling file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

export async function registerRoutes(app: Express): Promise<Server> {
  // API endpoint to receive and process video for lip reading
  app.post("/predict", upload.single("video"), (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No video file provided" });
      }

      console.log("Video received for prediction, size:", req.file.size);

      // In a real implementation, this would send the video to an AI service
      // for lip reading analysis. For now, we'll return a response immediately.
      
      // Skip the delay to make testing easier in the preview pane
      // Sample responses based on common medical phrases
      const predictions = [
        "I need water",
        "I need suctioning",
        "I'm in pain",
        "I need my medication",
        "I feel nauseous",
        "I can't breathe",
        "I'm feeling dizzy",
        "Can you call the nurse",
        "Help me please",
        "Thank you"
      ];
      
      // Select a random prediction
      const randomIndex = Math.floor(Math.random() * predictions.length);
      
      res.status(200).json({ 
        prediction: predictions[randomIndex],
        confidence: 0.87 // Mock confidence score
      });
      
    } catch (error) {
      console.error("Error processing video:", error);
      res.status(500).json({ message: "Error processing video" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
