import { useState, useEffect, useCallback } from 'react';

interface UseSpeechSynthesisReturn {
  speak: (text: string) => void;
  speaking: boolean;
  supported: boolean;
  volume: number;
  setVolume: (volume: number) => void;
}

const useSpeechSynthesis = (): UseSpeechSynthesisReturn => {
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [speaking, setSpeaking] = useState(false);
  const [supported, setSupported] = useState(false);
  const [volume, setVolume] = useState(1);

  useEffect(() => {
    if ('speechSynthesis' in window) {
      setSupported(true);
      
      // Load voices when available
      const loadVoices = () => {
        const availableVoices = window.speechSynthesis.getVoices();
        setVoices(availableVoices);
      };

      loadVoices();
      window.speechSynthesis.onvoiceschanged = loadVoices;
      
      return () => {
        window.speechSynthesis.onvoiceschanged = null;
      };
    }
  }, []);

  const speak = useCallback((text: string) => {
    if (!supported) return;

    // Cancel any ongoing speech
    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    
    // Set volume based on state
    utterance.volume = volume;
    
    // Try to use a female English voice if available
    const englishVoice = voices.find(
      (voice) => voice.lang.includes('en') && voice.name.includes('Female')
    );
    
    if (englishVoice) {
      utterance.voice = englishVoice;
    }

    // Set a reasonable rate of speech
    utterance.rate = 0.9;
    utterance.pitch = 1.1;

    utterance.onstart = () => setSpeaking(true);
    utterance.onend = () => setSpeaking(false);
    utterance.onerror = () => setSpeaking(false);

    window.speechSynthesis.speak(utterance);
  }, [supported, voices, volume]);

  return {
    speak,
    speaking,
    supported,
    volume,
    setVolume,
  };
};

export default useSpeechSynthesis;
