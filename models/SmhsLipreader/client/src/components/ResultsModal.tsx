import { FC, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Volume2, Video, RefreshCw } from "lucide-react";
import useSpeechSynthesis from "@/lib/useSpeechSynthesis";

interface ResultsModalProps {
  prediction: string;
  videoUrl: string | null;
  onClose: () => void;
  onRetry: () => void;
  onRecordAgain: () => void; // New prop for continuing to record
}

const ResultsModal: FC<ResultsModalProps> = ({
  prediction,
  videoUrl,
  onClose,
  onRetry,
  onRecordAgain,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { speak, speaking, volume, setVolume } = useSpeechSynthesis();
  
  // Auto-play video and speech when results modal opens
  useEffect(() => {
    // Play video automatically
    if (videoRef.current && videoUrl && videoUrl !== "about:blank") {
      videoRef.current.play().catch(e => console.error("Auto-play failed:", e));
    }
    
    // Play speech after a small delay to let video start first
    const timer = setTimeout(() => {
      speak(prediction);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [prediction, videoUrl, speak]);

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg w-full max-w-md mx-auto overflow-hidden">
        <div className="bg-primary text-white p-4">
          <h2 className="text-xl font-bold">Prediction Results</h2>
        </div>
        
        <div className="p-4">
          {/* Video playback */}
          <div className="aspect-w-9 aspect-h-16 mb-4 bg-gray-200 rounded-lg overflow-hidden">
            {videoUrl && videoUrl !== "about:blank" ? (
              <video
                ref={videoRef}
                src={videoUrl}
                className="w-full h-full object-cover"
                controls
                autoPlay
                playsInline
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500 text-center p-4">
                  [Preview Mode: Video recording placeholder]
                </p>
              </div>
            )}
          </div>
          
          {/* Prediction text */}
          <div className="mb-6">
            <h3 className="text-sm text-gray-500 uppercase tracking-wide mb-1">Predicted Phrase:</h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <p className="text-lg font-medium text-primary">{prediction}</p>
            </div>
          </div>
          
          {/* TTS Controls */}
          <div className="flex items-center justify-between mb-6">
            <Button
              onClick={() => speak(prediction)}
              disabled={speaking}
              className="flex items-center justify-center bg-primary text-white px-4 py-2 rounded-lg"
            >
              <Volume2 className="mr-2 h-4 w-4" />
              {speaking ? 'Playing...' : 'Play Audio'}
            </Button>
            
            <div className="flex items-center">
              <span className="mr-2 text-sm text-gray-600">Volume</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="w-24 accent-primary"
              />
            </div>
          </div>
          
          {/* Record Again Button */}
          <Button
            onClick={onRecordAgain}
            className="w-full mb-4 border border-green-600 bg-green-600 text-white font-medium py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
            style={{ cursor: 'pointer', pointerEvents: 'auto' }}
          >
            <Video className="mr-2 h-5 w-5" />
            Record Again
          </Button>
          
          {/* Action Buttons */}
          <div className="flex items-center justify-between gap-2 mb-4">
            <Button
              onClick={onRetry}
              variant="outline"
              className="flex-1 border border-primary text-primary font-medium py-3 px-2 rounded-lg hover:bg-primary/5 transition-colors"
              style={{ cursor: 'pointer', pointerEvents: 'auto' }}
              tabIndex={0}
              onKeyPress={(e) => e.key === 'Enter' && onRetry()}
            >
              <RefreshCw className="mr-1 h-4 w-4" />
              Try Again
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1 border border-gray-500 text-gray-500 font-medium py-3 px-2 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ cursor: 'pointer', pointerEvents: 'auto' }}
              tabIndex={0}
              onKeyPress={(e) => e.key === 'Enter' && onClose()}
            >
              <Video className="mr-1 h-4 w-4" />
              Record Again
            </Button>
          </div>
          
          {/* Close Button */}
          <div>
            <Button
              onClick={onClose}
              className="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary/90 transition-colors"
              style={{ cursor: 'pointer', pointerEvents: 'auto' }}
              tabIndex={0}
              onKeyPress={(e) => e.key === 'Enter' && onClose()}
            >
              Done
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsModal;
