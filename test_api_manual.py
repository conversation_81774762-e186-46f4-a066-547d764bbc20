#!/usr/bin/env python3
"""
Manual API testing script for the real-life test run
"""

import requests
import sys
from pathlib import Path
import time

def find_test_video():
    """Find a test video file"""
    test_dirs = ["test_webm_videos", "data", "videos", "debug_frames"]
    
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            for ext in ["*.mp4", "*.webm", "*.avi"]:
                videos = list(Path(test_dir).glob(ext))
                if videos:
                    return str(videos[0])
    return None

def test_api_endpoint(video_path, base_url="http://127.0.0.1:8000"):
    """Test the /predict_v2 endpoint"""
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return False
    
    print(f"Testing API with video: {video_path}")
    print(f"API endpoint: {base_url}/predict_v2")
    
    try:
        # Test if server is running
        response = requests.get(f"{base_url}/health", timeout=5)
        print("✅ Server is running")
    except requests.exceptions.RequestException:
        print("❌ Server is not running. Please start with:")
        print("   export VSR_IMPL=lightweight")
        print("   uvicorn backend.api.app:app --reload")
        return False
    
    try:
        # Send prediction request
        with open(video_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/predict_v2", files=files, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API test successful!")
            print("Response:")
            print(f"  Phrase: {result.get('phrase', 'N/A')}")
            print(f"  Confidence: {result.get('confidence', 'N/A')}")
            print(f"  Inference time: {result.get('inference_time_ms', 'N/A')} ms")
            return True
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Manual API Testing Script")
    print("=" * 40)
    
    # Check if video path provided
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        video_path = find_test_video()
        if not video_path:
            print("❌ No test video found. Please provide a video path:")
            print("   python test_api_manual.py /path/to/video.mp4")
            return
    
    print(f"Using video: {video_path}")
    
    # Test the API
    success = test_api_endpoint(video_path)
    
    if success:
        print("\n🎉 API test completed successfully!")
    else:
        print("\n❌ API test failed. Check the server logs.")

if __name__ == "__main__":
    main()
