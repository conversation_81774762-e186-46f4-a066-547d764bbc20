import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View } from 'react-native';
import SR<PERSON><PERSON>ipReader from './components/SRAVILipReader';
import CoreWordLipReader from './components/CoreWordLipReader';

export default function App() {
  return (
    <View style={styles.container}>
      <CoreWordLipReader />
      <StatusBar style="light" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});
