import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View } from 'react-native';
import SRAVILipReader from './components/SRAVILipReader';

export default function App() {
  return (
    <View style={styles.container}>
      <SRAVILipReader />
      <StatusBar style="light" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});
