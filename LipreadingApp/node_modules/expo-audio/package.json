{"name": "expo-audio", "title": "Expo Audio", "version": "0.4.9", "description": "A cross-platform audio library for React Native and Expo apps.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "homepage": "https://docs.expo.dev/versions/latest/sdk/audio/", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-audio"}, "keywords": ["react-native", "expo", "expo-audio", "audio", "ios", "android"], "author": "650 Industries, Inc.", "license": "MIT", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.10"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}, "gitHead": "85192ee6f5e76e0e81f91151c9c8aaca43c22c65"}