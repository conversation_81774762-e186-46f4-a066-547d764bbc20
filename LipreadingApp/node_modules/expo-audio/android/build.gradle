plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'expo.modules.audio'
version = '0.4.9'

android {
  namespace "expo.modules.audio"
  defaultConfig {
    versionCode 1
    versionName "0.4.9"
  }

  compileOptions {
    targetCompatibility JavaVersion.VERSION_1_8
  }
}

repositories {
  mavenCentral()
}

dependencies {
  implementation 'androidx.appcompat:appcompat:1.7.0'
  implementation("androidx.media3:media3-exoplayer:1.4.0")
  implementation("androidx.media3:media3-exoplayer-dash:1.4.0")
  implementation("androidx.media3:media3-datasource-okhttp:1.4.0")
  implementation("androidx.media3:media3-exoplayer-hls:1.4.0")
  implementation("androidx.media3:media3-exoplayer-smoothstreaming:1.4.0")
}
