// Configuration for the Lipreading App
// Update the API_BASE_URL to match your laptop's LAN IP address

export const CONFIG = {
  // Replace with your laptop's actual LAN IP address
  // You can find this by running: ifconfig | grep "inet " | grep -v 127.0.0.1
  API_BASE_URL: 'http://*************:8000',
  
  // Confidence thresholds for different modes
  CONFIDENCE_THRESHOLDS: {
    ICU: 0.65,
    OPEN: 0.55,
  },
  
  // Recording settings
  RECORDING: {
    DURATION: 3, // seconds (default, adjustable up to 8)
    MAX_DURATION: 8, // maximum allowed duration
    MIN_DURATION: 0.5, // minimum allowed duration
    QUALITY: '720p' as const,
    COUNTDOWN: 3, // seconds
  },
  
  // Speech settings
  SPEECH: {
    LANGUAGE: 'en-US',
    PITCH: 1.0,
    RATE: 0.8,
  },
  
  // API endpoints
  ENDPOINTS: {
    HEALTH: '/health',
    CTC_PREDICT: '/predict', // Updated to match simple backend
    CTC_PREDICT_ICU: '/predict', // Updated to match simple backend
    CTC_PREDICT_OPEN: '/predict', // Updated to match simple backend
    LIGHTWEIGHT_PREDICT: '/predict_v2', // Updated to use new lightweight endpoint
    CORE_WORD_PREDICT: '/predict_core_word', // New hybrid core word endpoint
    SPEAK_VIDEO: '/speak_video', // New server-side speaking video endpoint
  },
};

export default CONFIG;
