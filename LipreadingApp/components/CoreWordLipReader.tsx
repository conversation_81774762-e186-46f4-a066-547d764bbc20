import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Camera, CameraType } from 'expo-camera';
import { Audio } from 'expo-av';
import * as Speech from 'expo-speech';
import { Ionicons } from '@expo/vector-icons';
import { lipreadingAPI } from '../services/api';

const { width, height } = Dimensions.get('window');

interface PhraseExpansion {
  phrase: string;
  clinical_context: string;
  urgency: 'critical' | 'high' | 'medium' | 'low';
  common_usage: number;
  category: string;
}

interface CoreWordResult {
  success: boolean;
  core_word: string;
  confidence: number;
  clinical_priority: string;
  expansions: PhraseExpansion[];
  timestamp: string;
  inference_time_ms: number;
}

export default function CoreWordLipReader() {
  // Camera and recording state
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const cameraRef = useRef<Camera>(null);

  // Core word detection state
  const [coreWordResult, setCoreWordResult] = useState<CoreWordResult | null>(null);
  const [selectedPhrase, setSelectedPhrase] = useState<string | null>(null);
  const [showExpansions, setShowExpansions] = useState(false);

  // UI state
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    try {
      const cameraPermission = await Camera.requestCameraPermissionsAsync();
      const audioPermission = await Audio.requestPermissionsAsync();
      
      setHasPermission(
        cameraPermission.status === 'granted' && audioPermission.status === 'granted'
      );

      if (cameraPermission.status === 'granted' && audioPermission.status === 'granted') {
        console.log('Camera and audio permissions granted for core word recording');
        
        // Configure audio for recording
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
          playThroughEarpieceAndroid: false,
          staysActiveInBackground: false,
        });
      }
    } catch (error) {
      console.error('Permission request failed:', error);
      setHasPermission(false);
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current) return;

    try {
      setIsRecording(true);
      setRecordingDuration(0);
      setCoreWordResult(null);
      setShowExpansions(false);
      setSelectedPhrase(null);

      // Start recording timer
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => prev + 0.1);
      }, 100);

      console.log('Starting core word video recording');
      
      const video = await cameraRef.current.recordAsync({
        quality: '720p',
        maxDuration: 8, // 8 seconds max for core words
        mute: false,
      });

      console.log('Core word recording completed:', video.uri);
      await processVideo(video.uri);

    } catch (error) {
      console.error('Recording failed:', error);
      Alert.alert('Recording Error', 'Failed to record video. Please try again.');
    } finally {
      setIsRecording(false);
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    }
  };

  const stopRecording = async () => {
    if (!cameraRef.current || !isRecording) return;

    try {
      console.log('Stopping core word recording');
      await cameraRef.current.stopRecording();
    } catch (error) {
      console.error('Stop recording failed:', error);
    }
  };

  const processVideo = async (videoUri: string) => {
    setIsProcessing(true);

    try {
      console.log('Processing core word video:', videoUri);
      
      // Call core word prediction API
      const result = await lipreadingAPI.predictCoreWord(videoUri);
      
      console.log('Core word result:', result);
      setCoreWordResult(result);

      if (result.success && result.expansions.length > 0) {
        setShowExpansions(true);
        
        // Speak the detected core word
        Speech.speak(`Detected: ${result.core_word}`, {
          language: 'en-US',
          pitch: 1.0,
          rate: 0.8,
        });
      } else {
        Alert.alert(
          'Core Word Not Detected',
          'Please try again and speak one of the core words clearly: pillow, help, glasses, phone, doctor',
          [{ text: 'OK' }]
        );
      }

    } catch (error) {
      console.error('Core word processing error:', error);
      Alert.alert('Processing Error', 'Failed to process video. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const selectPhrase = async (expansion: PhraseExpansion) => {
    setSelectedPhrase(expansion.phrase);
    
    // Speak the selected phrase
    Speech.speak(expansion.phrase, {
      language: 'en-US',
      pitch: 1.0,
      rate: 0.8,
    });

    // Log the selection for clinical review
    console.log('Phrase selected:', {
      core_word: coreWordResult?.core_word,
      selected_phrase: expansion.phrase,
      clinical_context: expansion.clinical_context,
      urgency: expansion.urgency,
      timestamp: new Date().toISOString(),
    });

    // Show confirmation
    Alert.alert(
      'Phrase Selected',
      `"${expansion.phrase}" has been communicated.`,
      [
        { text: 'Record Another', onPress: resetSession },
        { text: 'OK' }
      ]
    );
  };

  const resetSession = () => {
    setCoreWordResult(null);
    setShowExpansions(false);
    setSelectedPhrase(null);
    setRecordingDuration(0);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#FF4444';
      case 'high': return '#FF8800';
      case 'medium': return '#FFAA00';
      case 'low': return '#00AA00';
      default: return '#666666';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'alert-circle';
      case 'high': return 'warning';
      case 'medium': return 'information-circle';
      case 'low': return 'checkmark-circle';
      default: return 'help-circle';
    }
  };

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Requesting permissions...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Camera and microphone access required</Text>
        <TouchableOpacity style={styles.retryButton} onPress={requestPermissions}>
          <Text style={styles.retryButtonText}>Grant Permissions</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>ICU Core Word Communication</Text>
        <Text style={styles.subtitle}>
          {showExpansions ? 'Select your intended phrase:' : 'Say: pillow, help, glasses, phone, doctor'}
        </Text>
      </View>

      {/* Camera View */}
      {!showExpansions && (
        <View style={styles.cameraContainer}>
          <Camera
            ref={cameraRef}
            style={styles.camera}
            type={CameraType.front}
            ratio="16:9"
          />
          
          {/* Recording overlay */}
          {isRecording && (
            <View style={styles.recordingOverlay}>
              <View style={styles.recordingIndicator}>
                <View style={styles.recordingDot} />
                <Text style={styles.recordingText}>
                  Recording: {recordingDuration.toFixed(1)}s
                </Text>
              </View>
            </View>
          )}
        </View>
      )}

      {/* Core Word Result */}
      {coreWordResult && !showExpansions && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Detected Core Word:</Text>
          <Text style={styles.coreWord}>{coreWordResult.core_word}</Text>
          <Text style={styles.confidence}>
            Confidence: {(coreWordResult.confidence * 100).toFixed(1)}%
          </Text>
        </View>
      )}

      {/* Phrase Expansions */}
      {showExpansions && coreWordResult && (
        <ScrollView style={styles.expansionsContainer}>
          <View style={styles.expansionsHeader}>
            <Text style={styles.expansionsTitle}>
              "{coreWordResult.core_word}" - Choose your phrase:
            </Text>
            <Text style={styles.expansionsSubtitle}>
              {coreWordResult.expansions.length} options available
            </Text>
          </View>

          {coreWordResult.expansions.map((expansion, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.expansionButton,
                { borderLeftColor: getUrgencyColor(expansion.urgency) }
              ]}
              onPress={() => selectPhrase(expansion)}
            >
              <View style={styles.expansionContent}>
                <View style={styles.expansionHeader}>
                  <Text style={styles.expansionPhrase}>{expansion.phrase}</Text>
                  <Ionicons
                    name={getUrgencyIcon(expansion.urgency)}
                    size={20}
                    color={getUrgencyColor(expansion.urgency)}
                  />
                </View>
                <Text style={styles.expansionContext}>
                  {expansion.clinical_context} • {expansion.urgency} priority
                </Text>
              </View>
            </TouchableOpacity>
          ))}

          <TouchableOpacity style={styles.resetButton} onPress={resetSession}>
            <Text style={styles.resetButtonText}>Record New Core Word</Text>
          </TouchableOpacity>
        </ScrollView>
      )}

      {/* Recording Controls */}
      {!showExpansions && (
        <View style={styles.controls}>
          {isProcessing ? (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <Text style={styles.processingText}>Processing core word...</Text>
            </View>
          ) : (
            <TouchableOpacity
              style={[styles.recordButton, isRecording && styles.recordButtonActive]}
              onPress={isRecording ? stopRecording : startRecording}
              disabled={isProcessing}
            >
              <Ionicons
                name={isRecording ? "stop" : "mic"}
                size={40}
                color="white"
              />
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  recordingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,0,0,0.8)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'white',
    marginRight: 8,
  },
  recordingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultContainer: {
    margin: 20,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 8,
  },
  coreWord: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  confidence: {
    fontSize: 16,
    color: '#666',
  },
  expansionsContainer: {
    flex: 1,
    padding: 20,
  },
  expansionsHeader: {
    marginBottom: 20,
    alignItems: 'center',
  },
  expansionsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  expansionsSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  expansionButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 12,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expansionContent: {
    padding: 16,
  },
  expansionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  expansionPhrase: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  expansionContext: {
    fontSize: 14,
    color: '#666',
  },
  controls: {
    padding: 20,
    alignItems: 'center',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  recordButtonActive: {
    backgroundColor: '#FF4444',
  },
  processingContainer: {
    alignItems: 'center',
  },
  processingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  resetButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  resetButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  errorText: {
    fontSize: 16,
    color: '#FF4444',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
