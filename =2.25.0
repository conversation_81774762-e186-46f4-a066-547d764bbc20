Collecting TTS==0.22.0
  Downloading TTS-0.22.0.tar.gz (1.7 MB)
     ━━ 1.… 4…  0:0…
        MB  M…      
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting ffmpeg-python==0.2.0
  Using cached ffmpeg_python-0.2.0-py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: requests in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (2.32.4)
Collecting numpy==1.22.0 (from TTS==0.22.0)
  Downloading numpy-1.22.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (2.0 kB)
Collecting cython>=0.29.30 (from TTS==0.22.0)
  Downloading cython-3.1.3-cp39-cp39-macosx_11_0_arm64.whl.metadata (4.7 kB)
Requirement already satisfied: scipy>=1.11.2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (1.13.1)
Requirement already satisfied: torch>=2.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (2.8.0)
Requirement already satisfied: torchaudio in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (2.8.0)
Requirement already satisfied: soundfile>=0.12.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (0.13.1)
Requirement already satisfied: librosa>=0.10.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (0.11.0)
Requirement already satisfied: scikit-learn>=1.3.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (1.6.1)
Requirement already satisfied: numba>=0.57.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (0.60.0)
Requirement already satisfied: inflect>=5.6.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (7.5.0)
Requirement already satisfied: tqdm>=4.64.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (4.67.1)
Collecting anyascii>=0.3.0 (from TTS==0.22.0)
  Downloading anyascii-0.3.3-py3-none-any.whl.metadata (1.6 kB)
Requirement already satisfied: pyyaml>=6.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (6.0.2)
Requirement already satisfied: fsspec>=2023.6.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (2025.7.0)
Collecting aiohttp>=3.8.1 (from TTS==0.22.0)
  Downloading aiohttp-3.12.15-cp39-cp39-macosx_11_0_arm64.whl.metadata (7.7 kB)
Requirement already satisfied: packaging>=23.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (25.0)
Requirement already satisfied: flask>=2.0.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (3.1.2)
Collecting pysbd>=0.3.4 (from TTS==0.22.0)
  Downloading pysbd-0.3.4-py3-none-any.whl.metadata (6.1 kB)
Collecting umap-learn>=0.5.1 (from TTS==0.22.0)
  Downloading umap_learn-0.5.9.post2-py3-none-any.whl.metadata (25 kB)
Collecting pandas<2.0,>=1.4 (from TTS==0.22.0)
  Downloading pandas-1.5.3-cp39-cp39-macosx_11_0_arm64.whl.metadata (11 kB)
Requirement already satisfied: matplotlib>=3.7.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (3.9.4)
Collecting trainer>=0.0.32 (from TTS==0.22.0)
  Downloading trainer-0.0.36-py3-none-any.whl.metadata (8.1 kB)
Collecting coqpit>=0.0.16 (from TTS==0.22.0)
  Downloading coqpit-0.0.17-py3-none-any.whl.metadata (11 kB)
Collecting jieba (from TTS==0.22.0)
  Downloading jieba-0.42.1.tar.gz (19.2 MB)
     ━━ 19… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pypinyin (from TTS==0.22.0)
  Downloading pypinyin-0.55.0-py2.py3-none-any.whl.metadata (12 kB)
Collecting hangul_romanize (from TTS==0.22.0)
  Downloading hangul_romanize-0.1.0-py3-none-any.whl.metadata (1.2 kB)
Collecting gruut==2.2.3 (from gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut-2.2.3.tar.gz (73 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting jamo (from TTS==0.22.0)
  Downloading jamo-0.4.1-py3-none-any.whl.metadata (2.3 kB)
Requirement already satisfied: nltk in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from TTS==0.22.0) (3.9.1)
Collecting g2pkk>=0.1.1 (from TTS==0.22.0)
  Downloading g2pkk-0.1.2-py3-none-any.whl.metadata (2.0 kB)
Collecting bangla (from TTS==0.22.0)
  Downloading bangla-0.0.5-py3-none-any.whl.metadata (4.7 kB)
Collecting bnnumerizer (from TTS==0.22.0)
  Downloading bnnumerizer-0.0.2.tar.gz (4.7 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting bnunicodenormalizer (from TTS==0.22.0)
  Downloading bnunicodenormalizer-0.1.7-py3-none-any.whl.metadata (22 kB)
Collecting einops>=0.6.0 (from TTS==0.22.0)
  Downloading einops-0.8.1-py3-none-any.whl.metadata (13 kB)
Collecting transformers>=4.33.0 (from TTS==0.22.0)
  Downloading transformers-4.55.4-py3-none-any.whl.metadata (41 kB)
Collecting encodec>=0.1.1 (from TTS==0.22.0)
  Downloading encodec-0.1.1.tar.gz (3.7 MB)
     ━━ 3.… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting unidecode>=1.3.2 (from TTS==0.22.0)
  Downloading Unidecode-1.4.0-py3-none-any.whl.metadata (13 kB)
Collecting num2words (from TTS==0.22.0)
  Downloading num2words-0.5.14-py3-none-any.whl.metadata (13 kB)
Collecting spacy>=3 (from spacy[ja]>=3->TTS==0.22.0)
  Downloading spacy-3.8.7-cp39-cp39-macosx_11_0_arm64.whl.metadata (27 kB)
Collecting future (from ffmpeg-python==0.2.0)
  Using cached future-1.0.0-py3-none-any.whl.metadata (4.0 kB)
Collecting Babel<3.0.0,>=2.8.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading babel-2.17.0-py3-none-any.whl.metadata (2.0 kB)
Collecting dateparser~=1.1.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading dateparser-1.1.8-py2.py3-none-any.whl.metadata (27 kB)
Collecting gruut-ipa<1.0,>=0.12.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut-ipa-0.13.0.tar.gz (101 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting gruut_lang_en~=2.0.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut_lang_en-2.0.1.tar.gz (15.3 MB)
     ━━ 15… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting jsonlines~=1.2.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading jsonlines-1.2.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting networkx<3.0.0,>=2.5.0 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading networkx-2.8.8-py3-none-any.whl.metadata (5.1 kB)
Collecting python-crfsuite~=0.9.7 (from gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading python_crfsuite-0.9.11-cp39-cp39-macosx_11_0_arm64.whl.metadata (4.3 kB)
Collecting gruut_lang_fr~=2.0.0 (from gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut_lang_fr-2.0.2.tar.gz (10.9 MB)
     ━━ 10… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting gruut_lang_es~=2.0.0 (from gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut_lang_es-2.0.1.tar.gz (31.4 MB)
     ━━ 31… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting gruut_lang_de~=2.0.0 (from gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading gruut_lang_de-2.0.1.tar.gz (18.1 MB)
     ━━ 18… 1…  0:0…
        MB  M…      
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: python-dateutil in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0) (2.9.0.post0)
Requirement already satisfied: pytz in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0) (2025.2)
Requirement already satisfied: regex!=2019.02.19,!=2021.8.27 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0) (2025.7.34)
Collecting tzlocal (from dateparser~=1.1.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0)
  Downloading tzlocal-5.3.1-py3-none-any.whl.metadata (7.6 kB)
Requirement already satisfied: six in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from jsonlines~=1.2.0->gruut==2.2.3->gruut[de,es,fr]==2.2.3->TTS==0.22.0) (1.17.0)
Collecting docopt>=0.6.2 (from num2words->TTS==0.22.0)
  Downloading docopt-0.6.2.tar.gz (25 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from requests) (3.4.3)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from requests) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from requests) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from requests) (2025.8.3)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.4.0 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)
Collecting async-timeout<6.0,>=4.0 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading async_timeout-5.0.1-py3-none-any.whl.metadata (5.1 kB)
Requirement already satisfied: attrs>=17.3.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from aiohttp>=3.8.1->TTS==0.22.0) (25.3.0)
Collecting frozenlist>=1.1.1 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading frozenlist-1.7.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (18 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading multidict-6.6.4-cp39-cp39-macosx_11_0_arm64.whl.metadata (5.3 kB)
Collecting propcache>=0.2.0 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading propcache-0.3.2-cp39-cp39-macosx_11_0_arm64.whl.metadata (12 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp>=3.8.1->TTS==0.22.0)
  Downloading yarl-1.20.1-cp39-cp39-macosx_11_0_arm64.whl.metadata (73 kB)
Requirement already satisfied: typing-extensions>=4.1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from multidict<7.0,>=4.5->aiohttp>=3.8.1->TTS==0.22.0) (4.14.1)
Requirement already satisfied: blinker>=1.9.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (1.9.0)
Requirement already satisfied: click>=8.1.3 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (8.1.8)
Requirement already satisfied: importlib-metadata>=3.6.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (8.7.0)
Requirement already satisfied: itsdangerous>=2.2.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (2.2.0)
Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (3.1.6)
Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (3.0.2)
Requirement already satisfied: werkzeug>=3.1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from flask>=2.0.1->TTS==0.22.0) (3.1.3)
Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from importlib-metadata>=3.6.0->flask>=2.0.1->TTS==0.22.0) (3.23.0)
Requirement already satisfied: more_itertools>=8.5.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from inflect>=5.6.0->TTS==0.22.0) (10.7.0)
Requirement already satisfied: typeguard>=4.0.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from inflect>=5.6.0->TTS==0.22.0) (4.4.4)
Requirement already satisfied: audioread>=2.1.9 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (3.0.1)
INFO: pip is looking at multiple versions of librosa to determine which version is compatible with other requirements. This could take a while.
Collecting librosa>=0.10.0 (from TTS==0.22.0)
  Downloading librosa-0.10.2.post1-py3-none-any.whl.metadata (8.6 kB)
  Downloading librosa-0.10.2-py3-none-any.whl.metadata (8.6 kB)
  Downloading librosa-0.10.1-py3-none-any.whl.metadata (8.3 kB)
  Downloading librosa-0.10.0.post2-py3-none-any.whl.metadata (8.3 kB)
  Downloading librosa-0.10.0.post1-py3-none-any.whl.metadata (8.3 kB)
  Downloading librosa-0.10.0-py3-none-any.whl.metadata (8.3 kB)
Requirement already satisfied: joblib>=0.14 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (1.5.1)
Requirement already satisfied: decorator>=4.3.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (5.2.1)
Requirement already satisfied: pooch>=1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (1.8.2)
Requirement already satisfied: soxr>=0.3.2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (0.5.0.post1)
Requirement already satisfied: lazy-loader>=0.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (0.4)
Requirement already satisfied: msgpack>=1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from librosa>=0.10.0->TTS==0.22.0) (1.1.1)
Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (1.3.0)
Requirement already satisfied: cycler>=0.10 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (4.59.0)
Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (1.4.7)
INFO: pip is looking at multiple versions of matplotlib to determine which version is compatible with other requirements. This could take a while.
Collecting matplotlib>=3.7.0 (from TTS==0.22.0)
  Using cached matplotlib-3.9.3-cp39-cp39-macosx_11_0_arm64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.2-cp39-cp39-macosx_11_0_arm64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.1.post1-cp39-cp39-macosx_11_0_arm64.whl.metadata (11 kB)
  Using cached matplotlib-3.9.0-cp39-cp39-macosx_11_0_arm64.whl.metadata (11 kB)
  Using cached matplotlib-3.8.4-cp39-cp39-macosx_11_0_arm64.whl.metadata (5.8 kB)
Requirement already satisfied: pillow>=8 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (11.3.0)
Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (3.2.3)
Requirement already satisfied: importlib-resources>=3.2.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from matplotlib>=3.7.0->TTS==0.22.0) (6.5.2)
INFO: pip is looking at multiple versions of contourpy to determine which version is compatible with other requirements. This could take a while.
Collecting contourpy>=1.0.1 (from matplotlib>=3.7.0->TTS==0.22.0)
  Using cached contourpy-1.2.1-cp39-cp39-macosx_11_0_arm64.whl.metadata (5.8 kB)
Requirement already satisfied: llvmlite<0.44,>=0.43.0dev0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from numba>=0.57.0->TTS==0.22.0) (0.43.0)
Requirement already satisfied: platformdirs>=2.5.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from pooch>=1.0->librosa>=0.10.0->TTS==0.22.0) (4.3.8)
Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from scikit-learn>=1.3.0->TTS==0.22.0) (3.6.0)
INFO: pip is looking at multiple versions of scipy to determine which version is compatible with other requirements. This could take a while.
Collecting scipy>=1.11.2 (from TTS==0.22.0)
  Using cached scipy-1.13.0-cp39-cp39-macosx_12_0_arm64.whl.metadata (60 kB)
  Using cached scipy-1.12.0-cp39-cp39-macosx_12_0_arm64.whl.metadata (60 kB)
  Using cached scipy-1.11.4-cp39-cp39-macosx_12_0_arm64.whl.metadata (60 kB)
Requirement already satisfied: cffi>=1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from soundfile>=0.12.0->TTS==0.22.0) (1.17.1)
Requirement already satisfied: pycparser in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from cffi>=1.0->soundfile>=0.12.0->TTS==0.22.0) (2.22)
Collecting spacy-legacy<3.1.0,>=3.0.11 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl.metadata (2.8 kB)
Collecting spacy-loggers<2.0.0,>=1.0.0 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading spacy_loggers-1.0.5-py3-none-any.whl.metadata (23 kB)
Collecting murmurhash<1.1.0,>=0.28.0 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading murmurhash-1.0.13-cp39-cp39-macosx_11_0_arm64.whl.metadata (2.2 kB)
Collecting cymem<2.1.0,>=2.0.2 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading cymem-2.0.11-cp39-cp39-macosx_11_0_arm64.whl.metadata (8.5 kB)
Collecting preshed<3.1.0,>=3.0.2 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading preshed-3.0.10-cp39-cp39-macosx_11_0_arm64.whl.metadata (2.4 kB)
Collecting thinc<8.4.0,>=8.3.4 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading thinc-8.3.6-cp39-cp39-macosx_11_0_arm64.whl.metadata (15 kB)
Collecting wasabi<1.2.0,>=0.9.1 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading wasabi-1.1.3-py3-none-any.whl.metadata (28 kB)
Collecting srsly<3.0.0,>=2.4.3 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading srsly-2.5.1-cp39-cp39-macosx_11_0_arm64.whl.metadata (19 kB)
Collecting catalogue<2.1.0,>=2.0.6 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading catalogue-2.0.10-py3-none-any.whl.metadata (14 kB)
Collecting weasel<0.5.0,>=0.1.0 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading weasel-0.4.1-py3-none-any.whl.metadata (4.6 kB)
Collecting typer<1.0.0,>=0.3.0 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading typer-0.16.1-py3-none-any.whl.metadata (15 kB)
Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from spacy>=3->spacy[ja]>=3->TTS==0.22.0) (2.11.7)
Requirement already satisfied: setuptools in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from spacy>=3->spacy[ja]>=3->TTS==0.22.0) (80.9.0)
Collecting langcodes<4.0.0,>=3.2.0 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading langcodes-3.5.0-py3-none-any.whl.metadata (29 kB)
Collecting language-data>=1.2 (from langcodes<4.0.0,>=3.2.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading language_data-1.3.0-py3-none-any.whl.metadata (4.3 kB)
Requirement already satisfied: annotated-types>=0.6.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (0.4.1)
Collecting blis<1.4.0,>=1.3.0 (from thinc<8.4.0,>=8.3.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading blis-1.3.0.tar.gz (2.5 MB)
     ━━ 2.… 3…  0:0…
        MB  M…      
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting confection<1.0.0,>=0.0.1 (from thinc<8.4.0,>=8.3.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading confection-0.1.5-py3-none-any.whl.metadata (19 kB)
INFO: pip is looking at multiple versions of thinc to determine which version is compatible with other requirements. This could take a while.
Collecting thinc<8.4.0,>=8.3.4 (from spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading thinc-8.3.4-cp39-cp39-macosx_11_0_arm64.whl.metadata (15 kB)
Collecting blis<1.3.0,>=1.2.0 (from thinc<8.4.0,>=8.3.4->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading blis-1.2.1.tar.gz (2.5 MB)
     ━━ 2.… 1…  0:0…
        MB  M…      
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting shellingham>=1.3.0 (from typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Requirement already satisfied: rich>=10.11.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (14.1.0)
Collecting cloudpathlib<1.0.0,>=0.7.0 (from weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading cloudpathlib-0.21.1-py3-none-any.whl.metadata (14 kB)
Collecting smart-open<8.0.0,>=5.2.1 (from weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading smart_open-7.3.0.post1-py3-none-any.whl.metadata (24 kB)
Requirement already satisfied: wrapt in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from smart-open<8.0.0,>=5.2.1->weasel<0.5.0,>=0.1.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (1.17.2)
Collecting marisa-trie>=1.1.0 (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0)
  Downloading marisa_trie-1.3.1-cp39-cp39-macosx_11_0_arm64.whl.metadata (10 kB)
Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (2.19.2)
Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy>=3->spacy[ja]>=3->TTS==0.22.0) (0.1.2)
Collecting sudachipy!=0.6.1,>=0.5.2 (from spacy[ja]>=3->TTS==0.22.0)
  Downloading SudachiPy-0.6.10-cp39-cp39-macosx_10_12_universal2.whl.metadata (12 kB)
Collecting sudachidict_core>=20211220 (from spacy[ja]>=3->TTS==0.22.0)
  Downloading sudachidict_core-20250515-py3-none-any.whl.metadata (2.7 kB)
Requirement already satisfied: filelock in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from torch>=2.1->TTS==0.22.0) (3.19.1)
Requirement already satisfied: sympy>=1.13.3 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from torch>=2.1->TTS==0.22.0) (1.14.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from sympy>=1.13.3->torch>=2.1->TTS==0.22.0) (1.3.0)
Collecting psutil (from trainer>=0.0.32->TTS==0.22.0)
  Downloading psutil-7.0.0-cp36-abi3-macosx_11_0_arm64.whl.metadata (22 kB)
Requirement already satisfied: tensorboard in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from trainer>=0.0.32->TTS==0.22.0) (2.19.0)
Collecting huggingface-hub<1.0,>=0.34.0 (from transformers>=4.33.0->TTS==0.22.0)
  Downloading huggingface_hub-0.34.4-py3-none-any.whl.metadata (14 kB)
Collecting tokenizers<0.22,>=0.21 (from transformers>=4.33.0->TTS==0.22.0)
  Downloading tokenizers-0.21.4-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.7 kB)
Collecting safetensors>=0.4.3 (from transformers>=4.33.0->TTS==0.22.0)
  Downloading safetensors-0.6.2-cp38-abi3-macosx_11_0_arm64.whl.metadata (4.1 kB)
Collecting hf-xet<2.0.0,>=1.1.3 (from huggingface-hub<1.0,>=0.34.0->transformers>=4.33.0->TTS==0.22.0)
  Downloading hf_xet-1.1.8-cp37-abi3-macosx_11_0_arm64.whl.metadata (703 bytes)
INFO: pip is looking at multiple versions of umap-learn to determine which version is compatible with other requirements. This could take a while.
Collecting umap-learn>=0.5.1 (from TTS==0.22.0)
  Downloading umap_learn-0.5.8-py3-none-any.whl.metadata (23 kB)
  Downloading umap_learn-0.5.7-py3-none-any.whl.metadata (21 kB)
Collecting pynndescent>=0.5 (from umap-learn>=0.5.1->TTS==0.22.0)
  Downloading pynndescent-0.5.13-py3-none-any.whl.metadata (6.8 kB)
Requirement already satisfied: absl-py>=0.4 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from tensorboard->trainer>=0.0.32->TTS==0.22.0) (2.3.1)
Requirement already satisfied: grpcio>=1.48.2 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from tensorboard->trainer>=0.0.32->TTS==0.22.0) (1.74.0)
Requirement already satisfied: markdown>=2.6.8 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from tensorboard->trainer>=0.0.32->TTS==0.22.0) (3.8.2)
Requirement already satisfied: protobuf!=4.24.0,>=3.19.6 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from tensorboard->trainer>=0.0.32->TTS==0.22.0) (4.25.8)
Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /Users/<USER>/Desktop/app dev 23.5.25/.venv/lib/python3.9/site-packages (from tensorboard->trainer>=0.0.32->TTS==0.22.0) (0.7.2)
Using cached ffmpeg_python-0.2.0-py3-none-any.whl (25 kB)
Downloading numpy-1.22.0-cp39-cp39-macosx_11_0_arm64.whl (12.8 MB)
   ━━━ 12… 3.8  0:0…
       MB  MB…      
Downloading babel-2.17.0-py3-none-any.whl (10.2 MB)
   ━━━ 10… 10…  0:0…
       MB  MB…      
Downloading dateparser-1.1.8-py2.py3-none-any.whl (293 kB)
Downloading jsonlines-1.2.0-py2.py3-none-any.whl (7.6 kB)
Downloading networkx-2.8.8-py3-none-any.whl (2.0 MB)
   ━━━ 2.… 12…  0:0…
       MB  MB…      
Downloading num2words-0.5.14-py3-none-any.whl (163 kB)
Downloading pandas-1.5.3-cp39-cp39-macosx_11_0_arm64.whl (11.0 MB)
   ━━━ 11… 3.7  0:0…
       MB  MB…      
Downloading python_crfsuite-0.9.11-cp39-cp39-macosx_11_0_arm64.whl (319 kB)
Downloading aiohttp-3.12.15-cp39-cp39-macosx_11_0_arm64.whl (469 kB)
Downloading async_timeout-5.0.1-py3-none-any.whl (6.2 kB)
Downloading multidict-6.6.4-cp39-cp39-macosx_11_0_arm64.whl (44 kB)
Downloading yarl-1.20.1-cp39-cp39-macosx_11_0_arm64.whl (89 kB)
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Downloading anyascii-0.3.3-py3-none-any.whl (345 kB)
Downloading coqpit-0.0.17-py3-none-any.whl (13 kB)
Using cached cython-3.1.3-cp39-cp39-macosx_11_0_arm64.whl (2.8 MB)
Downloading einops-0.8.1-py3-none-any.whl (64 kB)
Downloading frozenlist-1.7.0-cp39-cp39-macosx_11_0_arm64.whl (47 kB)
Downloading g2pkk-0.1.2-py3-none-any.whl (25 kB)
Downloading librosa-0.10.0-py3-none-any.whl (252 kB)
Using cached matplotlib-3.8.4-cp39-cp39-macosx_11_0_arm64.whl (7.5 MB)
Using cached contourpy-1.2.1-cp39-cp39-macosx_11_0_arm64.whl (244 kB)
Downloading propcache-0.3.2-cp39-cp39-macosx_11_0_arm64.whl (43 kB)
Downloading pysbd-0.3.4-py3-none-any.whl (71 kB)
Downloading scipy-1.11.4-cp39-cp39-macosx_12_0_arm64.whl (29.7 MB)
   ━━━ 29… 3.4  0:0…
       MB  MB…      
Downloading spacy-3.8.7-cp39-cp39-macosx_11_0_arm64.whl (6.3 MB)
   ━━━ 6.… 10…  0:0…
       MB  MB…      
Downloading catalogue-2.0.10-py3-none-any.whl (17 kB)
Downloading cymem-2.0.11-cp39-cp39-macosx_11_0_arm64.whl (42 kB)
Downloading langcodes-3.5.0-py3-none-any.whl (182 kB)
Downloading murmurhash-1.0.13-cp39-cp39-macosx_11_0_arm64.whl (26 kB)
Downloading preshed-3.0.10-cp39-cp39-macosx_11_0_arm64.whl (129 kB)
Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl (29 kB)
Downloading spacy_loggers-1.0.5-py3-none-any.whl (22 kB)
Downloading srsly-2.5.1-cp39-cp39-macosx_11_0_arm64.whl (635 kB)
   ━━━ 63… 11…  0:0…
       kB  MB…      
Downloading thinc-8.3.4-cp39-cp39-macosx_11_0_arm64.whl (780 kB)
   ━━━ 78… 11…  0:0…
       kB  MB…      
Downloading confection-0.1.5-py3-none-any.whl (35 kB)
Downloading typer-0.16.1-py3-none-any.whl (46 kB)
Downloading wasabi-1.1.3-py3-none-any.whl (27 kB)
Downloading weasel-0.4.1-py3-none-any.whl (50 kB)
Downloading cloudpathlib-0.21.1-py3-none-any.whl (52 kB)
Downloading smart_open-7.3.0.post1-py3-none-any.whl (61 kB)
Downloading language_data-1.3.0-py3-none-any.whl (5.4 MB)
   ━━━ 5.… 13…  0:0…
       MB  MB…      
Downloading marisa_trie-1.3.1-cp39-cp39-macosx_11_0_arm64.whl (157 kB)
Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Downloading sudachidict_core-20250515-py3-none-any.whl (72.1 MB)
   ━━━ 72… 11…  0:0…
       MB  MB…      
Downloading SudachiPy-0.6.10-cp39-cp39-macosx_10_12_universal2.whl (3.0 MB)
   ━━━ 3.… 4.4  0:0…
       MB  MB…      
Downloading trainer-0.0.36-py3-none-any.whl (51 kB)
Downloading transformers-4.55.4-py3-none-any.whl (11.3 MB)
   ━━━ 11… 10…  0:0…
       MB  MB…      
Downloading huggingface_hub-0.34.4-py3-none-any.whl (561 kB)
   ━━━ 56… 11…  0:0…
       kB  MB…      
Downloading hf_xet-1.1.8-cp37-abi3-macosx_11_0_arm64.whl (2.6 MB)
   ━━━ 2.… 10…  0:0…
       MB  MB…      
Downloading tokenizers-0.21.4-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)
   ━━━ 2.… 8.4  0:0…
       MB  MB…      
Downloading safetensors-0.6.2-cp38-abi3-macosx_11_0_arm64.whl (432 kB)
Downloading umap_learn-0.5.7-py3-none-any.whl (88 kB)
Downloading pynndescent-0.5.13-py3-none-any.whl (56 kB)
Downloading Unidecode-1.4.0-py3-none-any.whl (235 kB)
Downloading bangla-0.0.5-py3-none-any.whl (5.1 kB)
Downloading bnunicodenormalizer-0.1.7-py3-none-any.whl (23 kB)
Using cached future-1.0.0-py3-none-any.whl (491 kB)
Downloading hangul_romanize-0.1.0-py3-none-any.whl (4.6 kB)
Downloading jamo-0.4.1-py3-none-any.whl (9.5 kB)
Downloading psutil-7.0.0-cp36-abi3-macosx_11_0_arm64.whl (239 kB)
Downloading pypinyin-0.55.0-py2.py3-none-any.whl (840 kB)
   ━━━ 84… 4.0  0:0…
       kB  MB…      
Downloading tzlocal-5.3.1-py3-none-any.whl (18 kB)
Building wheels for collected packages: TTS, gruut, gruut-ipa, gruut_lang_de, gruut_lang_en, gruut_lang_es, gruut_lang_fr, docopt, encodec, blis, bnnumerizer, jieba
  Building wheel for TTS (pyproject.toml): started
  Building wheel for TTS (pyproject.toml): finished with status 'done'
  Created wheel for TTS: filename=tts-0.22.0-cp39-cp39-macosx_10_9_universal2.whl size=904111 sha256=ff32e940ca2799632f5ad3c9e770015433133a77c54d17c0726f214f33eb4f49
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/e9/94/e7/52e526c3ef9c07ac0b67a7dce87f81b6fb83ffd2d1754224e3
  Building wheel for gruut (setup.py): started
  Building wheel for gruut (setup.py): finished with status 'done'
  Created wheel for gruut: filename=gruut-2.2.3-py3-none-any.whl size=75885 sha256=368701654f7623c5a8258e6f89b5ed42ec09a3c434f7544974f83544472025d3
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/2e/9c/b4/a963ba6c181aa3d65370c3aa40c420a249ff02cef4c724c071
  Building wheel for gruut-ipa (setup.py): started
  Building wheel for gruut-ipa (setup.py): finished with status 'done'
  Created wheel for gruut-ipa: filename=gruut_ipa-0.13.0-py3-none-any.whl size=104963 sha256=83949b01e1aa5ab9fd0d782dae38de94579153f18a4df5f9752c48aad1502908
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/ab/22/10/9d98cfd0277a5876d450ca23902ecd0a41a5473efdcf309e6e
  Building wheel for gruut_lang_de (setup.py): started
  Building wheel for gruut_lang_de (setup.py): finished with status 'done'
  Created wheel for gruut_lang_de: filename=gruut_lang_de-2.0.1-py3-none-any.whl size=18498355 sha256=1b5961784da761100fb2d73dcec0780fa13cd1a234aca984e7fd91fa5cbf29b2
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/f7/7d/ef/3e3f9d7a50d9ef00d61a52e57c4d2a7fde883d15c41ee9ceb4
  Building wheel for gruut_lang_en (setup.py): started
  Building wheel for gruut_lang_en (setup.py): finished with status 'done'
  Created wheel for gruut_lang_en: filename=gruut_lang_en-2.0.1-py3-none-any.whl size=15326897 sha256=1af08f9d328ed1b74bab94120444812b3062b1ddf6f0098c954c9c540a69fbf8
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/29/48/ce/398bfc2c5d2801c08750c07255b3fea56ac84ee2625c61a17c
  Building wheel for gruut_lang_es (setup.py): started
  Building wheel for gruut_lang_es (setup.py): finished with status 'done'
  Created wheel for gruut_lang_es: filename=gruut_lang_es-2.0.1-py3-none-any.whl size=32173971 sha256=5625088812d2ffcc786cc47a31bc95de9495be2408361914bba7c57b896dc957
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/bb/9a/7f/0bf628d77790fc5ed2d9d9471223cf885d4d34d3d4fe9f5789
  Building wheel for gruut_lang_fr (setup.py): started
  Building wheel for gruut_lang_fr (setup.py): finished with status 'done'
  Created wheel for gruut_lang_fr: filename=gruut_lang_fr-2.0.2-py3-none-any.whl size=10968809 sha256=9c2a5ad5718d7aad58f465e098e9c18da335f1d404bdb46e60403af24de7db98
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/ef/0c/63/ff8472d618329f72f6f645e85e4e1584ea1dd4cb9c0fc2fc06
  Building wheel for docopt (setup.py): started
  Building wheel for docopt (setup.py): finished with status 'done'
  Created wheel for docopt: filename=docopt-0.6.2-py2.py3-none-any.whl size=13783 sha256=536f9b5e16bc7192d55fbcc87195c628c34d94540492c845918c9ca227ed3e4b
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/70/4a/46/1309fc853b8d395e60bafaf1b6df7845bdd82c95fd59dd8d2b
  Building wheel for encodec (setup.py): started
  Building wheel for encodec (setup.py): finished with status 'done'
  Created wheel for encodec: filename=encodec-0.1.1-py3-none-any.whl size=45853 sha256=db809a264eb130e07b56866cbf52941b72c7003abd8d651d3b1d34ca67666774
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/1d/9d/20/489d6aafffb505e18fcfcfbe722562f91c26af0a8a6da7d00b
  Building wheel for blis (pyproject.toml): started
  Building wheel for blis (pyproject.toml): finished with status 'done'
  Created wheel for blis: filename=blis-1.2.1-cp39-cp39-macosx_10_9_universal2.whl size=1496650 sha256=8218b11523172d08da6f1d7b57dd30387a56b04f38906322fa917d5b06bdaa4d
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/81/f6/9a/67b794f1da49a322bdb94b1a2b4e03d185d9580f5e100edab2
  Building wheel for bnnumerizer (setup.py): started
  Building wheel for bnnumerizer (setup.py): finished with status 'done'
  Created wheel for bnnumerizer: filename=bnnumerizer-0.0.2-py3-none-any.whl size=5339 sha256=41d8724766ed4352a16c0d0458c489f599817c02004d6f7cfa8aba2b574641b1
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/cf/2f/42/a7c0e5bc7281b2e1b7bdfbd08d8220010b6df91ffdcfb571e7
  Building wheel for jieba (setup.py): started
  Building wheel for jieba (setup.py): finished with status 'done'
  Created wheel for jieba: filename=jieba-0.42.1-py3-none-any.whl size=19314509 sha256=982629e28ae42b8358d97d73c2d6a4b37fb707e975ef881aece258d5b50bd494
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/7d/74/cf/08c94db4b784e2c1ef675a600b7b5b281fd25240dcb954ee7e
Successfully built TTS gruut gruut-ipa gruut_lang_de gruut_lang_en gruut_lang_es gruut_lang_fr docopt encodec blis bnnumerizer jieba
Installing collected packages: sudachipy, jieba, jamo, hangul_romanize, gruut_lang_fr, gruut_lang_es, gruut_lang_en, gruut_lang_de, docopt, cymem, bnunicodenormalizer, bnnumerizer, bangla, wasabi, unidecode, tzlocal, sudachidict_core, spacy-loggers, spacy-legacy, smart-open, shellingham, safetensors, python-crfsuite, pysbd, pypinyin, psutil, propcache, numpy, num2words, networkx, murmurhash, multidict, marisa-trie, jsonlines, hf-xet, gruut-ipa, future, frozenlist, einops, cython, coqpit, cloudpathlib, catalogue, Babel, async-timeout, anyascii, aiohappyeyeballs, yarl, srsly, scipy, preshed, pandas, language-data, huggingface-hub, g2pkk, ffmpeg-python, dateparser, contourpy, blis, aiosignal, typer, tokenizers, matplotlib, langcodes, gruut, confection, aiohttp, weasel, transformers, trainer, thinc, pynndescent, librosa, encodec, umap-learn, spacy, TTS
  Attempting uninstall: numpy
    Found existing installation: numpy 2.0.2
    Uninstalling numpy-2.0.2:
      Successfully uninstalled numpy-2.0.2
  Attempting uninstall: networkx
    Found existing installation: networkx 3.2.1
    Uninstalling networkx-3.2.1:
      Successfully uninstalled networkx-3.2.1
  Attempting uninstall: scipy
    Found existing installation: scipy 1.13.1
    Uninstalling scipy-1.13.1:
      Successfully uninstalled scipy-1.13.1
  Attempting uninstall: pandas
    Found existing installation: pandas 2.3.1
    Uninstalling pandas-2.3.1:
      Successfully uninstalled pandas-2.3.1
  Attempting uninstall: contourpy
    Found existing installation: contourpy 1.3.0
    Uninstalling contourpy-1.3.0:
      Successfully uninstalled contourpy-1.3.0
  Attempting uninstall: matplotlib
    Found existing installation: matplotlib 3.9.4
    Uninstalling matplotlib-3.9.4:
      Successfully uninstalled matplotlib-3.9.4
  Attempting uninstall: librosa
    Found existing installation: librosa 0.11.0
    Uninstalling librosa-0.11.0:
      Successfully uninstalled librosa-0.11.0

Successfully installed Babel-2.17.0 TTS-0.22.0 aiohappyeyeballs-2.6.1 aiohttp-3.12.15 aiosignal-1.4.0 anyascii-0.3.3 async-timeout-5.0.1 bangla-0.0.5 blis-1.2.1 bnnumerizer-0.0.2 bnunicodenormalizer-0.1.7 catalogue-2.0.10 cloudpathlib-0.21.1 confection-0.1.5 contourpy-1.2.1 coqpit-0.0.17 cymem-2.0.11 cython-3.1.3 dateparser-1.1.8 docopt-0.6.2 einops-0.8.1 encodec-0.1.1 ffmpeg-python-0.2.0 frozenlist-1.7.0 future-1.0.0 g2pkk-0.1.2 gruut-2.2.3 gruut-ipa-0.13.0 gruut_lang_de-2.0.1 gruut_lang_en-2.0.1 gruut_lang_es-2.0.1 gruut_lang_fr-2.0.2 hangul_romanize-0.1.0 hf-xet-1.1.8 huggingface-hub-0.34.4 jamo-0.4.1 jieba-0.42.1 jsonlines-1.2.0 langcodes-3.5.0 language-data-1.3.0 librosa-0.10.0 marisa-trie-1.3.1 matplotlib-3.8.4 multidict-6.6.4 murmurhash-1.0.13 networkx-2.8.8 num2words-0.5.14 numpy-1.22.0 pandas-1.5.3 preshed-3.0.10 propcache-0.3.2 psutil-7.0.0 pynndescent-0.5.13 pypinyin-0.55.0 pysbd-0.3.4 python-crfsuite-0.9.11 safetensors-0.6.2 scipy-1.11.4 shellingham-1.5.4 smart-open-7.3.0.post1 spacy-3.8.7 spacy-legacy-3.0.12 spacy-loggers-1.0.5 srsly-2.5.1 sudachidict_core-20250515 sudachipy-0.6.10 thinc-8.3.4 tokenizers-0.21.4 trainer-0.0.36 transformers-4.55.4 typer-0.16.1 tzlocal-5.3.1 umap-learn-0.5.7 unidecode-1.4.0 wasabi-1.1.3 weasel-0.4.1 yarl-1.20.1
