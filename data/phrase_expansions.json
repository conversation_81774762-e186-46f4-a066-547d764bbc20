{"phrase_expansions": {"pillow": {"category": "comfort_positioning", "clinical_priority": "medium", "expansions": [{"phrase": "I need a pillow", "clinical_context": "basic_comfort", "urgency": "low", "common_usage": 0.9}, {"phrase": "Please adjust my pillow", "clinical_context": "positioning_comfort", "urgency": "low", "common_usage": 0.8}, {"phrase": "Remove the pillow", "clinical_context": "discomfort_positioning", "urgency": "medium", "common_usage": 0.6}, {"phrase": "Pillow is uncomfortable", "clinical_context": "comfort_complaint", "urgency": "low", "common_usage": 0.7}, {"phrase": "More pillows please", "clinical_context": "positioning_support", "urgency": "low", "common_usage": 0.5}, {"phrase": "Pillow too high", "clinical_context": "positioning_adjustment", "urgency": "low", "common_usage": 0.6}, {"phrase": "Pillow under my knees", "clinical_context": "positioning_specific", "urgency": "low", "common_usage": 0.4}, {"phrase": "Pillow behind my back", "clinical_context": "positioning_support", "urgency": "low", "common_usage": 0.5}]}, "help": {"category": "assistance_request", "clinical_priority": "high", "expansions": [{"phrase": "I need help", "clinical_context": "general_assistance", "urgency": "high", "common_usage": 0.95}, {"phrase": "Help me sit up", "clinical_context": "positioning_assistance", "urgency": "medium", "common_usage": 0.8}, {"phrase": "Help me turn over", "clinical_context": "positioning_assistance", "urgency": "medium", "common_usage": 0.7}, {"phrase": "Call for help", "clinical_context": "emergency_assistance", "urgency": "high", "common_usage": 0.6}, {"phrase": "Help with pain", "clinical_context": "pain_management", "urgency": "high", "common_usage": 0.8}, {"phrase": "Help me breathe", "clinical_context": "respiratory_distress", "urgency": "critical", "common_usage": 0.4}, {"phrase": "Help me get comfortable", "clinical_context": "comfort_assistance", "urgency": "medium", "common_usage": 0.6}, {"phrase": "I need immediate help", "clinical_context": "urgent_assistance", "urgency": "critical", "common_usage": 0.3}]}, "glasses": {"category": "personal_items", "clinical_priority": "low", "expansions": [{"phrase": "I need my glasses", "clinical_context": "personal_items", "urgency": "low", "common_usage": 0.9}, {"phrase": "Where are my glasses", "clinical_context": "item_location", "urgency": "low", "common_usage": 0.8}, {"phrase": "Please get my glasses", "clinical_context": "item_request", "urgency": "low", "common_usage": 0.7}, {"phrase": "My glasses are broken", "clinical_context": "item_problem", "urgency": "low", "common_usage": 0.3}, {"phrase": "Clean my glasses", "clinical_context": "item_maintenance", "urgency": "low", "common_usage": 0.4}, {"phrase": "Glasses are uncomfortable", "clinical_context": "comfort_complaint", "urgency": "low", "common_usage": 0.3}, {"phrase": "I can't see without glasses", "clinical_context": "vision_problem", "urgency": "medium", "common_usage": 0.6}, {"phrase": "Put my glasses on", "clinical_context": "assistance_request", "urgency": "low", "common_usage": 0.5}]}, "phone": {"category": "communication", "clinical_priority": "medium", "expansions": [{"phrase": "I need my phone", "clinical_context": "communication_request", "urgency": "medium", "common_usage": 0.8}, {"phrase": "Call my family", "clinical_context": "family_contact", "urgency": "medium", "common_usage": 0.9}, {"phrase": "Phone my wife", "clinical_context": "spouse_contact", "urgency": "medium", "common_usage": 0.7}, {"phrase": "Phone my husband", "clinical_context": "spouse_contact", "urgency": "medium", "common_usage": 0.7}, {"phrase": "Where is my phone", "clinical_context": "item_location", "urgency": "low", "common_usage": 0.6}, {"phrase": "Phone is not working", "clinical_context": "technical_problem", "urgency": "low", "common_usage": 0.3}, {"phrase": "Help me use the phone", "clinical_context": "assistance_request", "urgency": "medium", "common_usage": 0.5}, {"phrase": "Phone my children", "clinical_context": "family_contact", "urgency": "medium", "common_usage": 0.6}]}, "doctor": {"category": "medical_staff", "clinical_priority": "high", "expansions": [{"phrase": "I need the doctor", "clinical_context": "medical_consultation", "urgency": "high", "common_usage": 0.9}, {"phrase": "Call the doctor", "clinical_context": "medical_emergency", "urgency": "high", "common_usage": 0.8}, {"phrase": "When will doctor come", "clinical_context": "schedule_inquiry", "urgency": "medium", "common_usage": 0.7}, {"phrase": "Doctor said what", "clinical_context": "information_clarification", "urgency": "medium", "common_usage": 0.6}, {"phrase": "Talk to my doctor", "clinical_context": "communication_request", "urgency": "medium", "common_usage": 0.5}, {"phrase": "Doctor needs to know", "clinical_context": "information_sharing", "urgency": "high", "common_usage": 0.4}, {"phrase": "Where is the doctor", "clinical_context": "staff_location", "urgency": "medium", "common_usage": 0.5}, {"phrase": "Doctor please come now", "clinical_context": "urgent_consultation", "urgency": "critical", "common_usage": 0.3}]}}, "expansion_metadata": {"version": "1.0", "created_date": "2025-01-30", "clinical_validation": "pending", "total_expansions": 40, "average_per_word": 8, "urgency_distribution": {"critical": 3, "high": 8, "medium": 15, "low": 14}, "clinical_categories": ["comfort_positioning", "assistance_request", "personal_items", "communication", "medical_staff"]}, "usage_guidelines": {"selection_criteria": ["Present expansions sorted by common_usage score", "Highlight high urgency items with visual indicators", "Group by clinical_context when appropriate", "Limit display to top 6-8 most relevant expansions"], "clinical_safety": ["All expansions must be clinically appropriate", "Critical urgency items should be prominently displayed", "Include fallback option for custom phrase input", "Log all selections for clinical review"], "personalization": ["Allow custom phrase additions per patient", "Track usage patterns for optimization", "Support context-aware filtering (time of day, patient condition)", "Enable nurse/caregiver phrase validation"]}}