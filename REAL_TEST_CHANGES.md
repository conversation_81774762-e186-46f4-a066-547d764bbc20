# Real-Life Test Run Changes

This document summarizes all the changes made to support the real-life test run with frozen encoder + trainable head training.

## Changes Made

### 1. Enhanced `predict_phrase` function (`backend/lightweight_vsr/infer.py`)

**Added support for:**
- `art_dir` parameter to load models from artifact directories
- Automatic detection of `best.pt` or `model.ts` files
- `confidence_threshold` parameter to override default threshold
- Support for `.pt` files in addition to `.ckpt` and `.ts` files

**Key changes:**
```python
def predict_phrase(video_path: Union[str, Path],
                  model_path: str = None,
                  config_path: str = None,
                  art_dir: str = None,
                  confidence_threshold: float = None) -> Dict:
```

### 2. Updated API endpoint (`backend/api/app.py`)

**Modified `predict_lightweight` function to:**
- Use the new `art_dir` parameter
- Point to `artifacts/vsr_fasthead_v1` for the fast-trained model

```python
def predict_lightweight(video_path):
    """Wrapper for lightweight model prediction"""
    # Use the new fast-trained head model
    return predict_lightweight_base(
        video_path,
        art_dir="artifacts/vsr_fasthead_v1"
    )
```

### 3. Enhanced training script (`backend/lightweight_vsr/train.py`)

**Added command-line parameters:**
- `--frames`: Number of frames to use
- `--size`: Frame size 
- `--min_per_phrase`: Minimum samples per phrase
- `--max_per_phrase`: Maximum samples per phrase
- `--freeze_encoder`: Freeze encoder and train only classifier head

**Added frozen encoder support:**
- Freezes `conv_blocks` and `gru` parameters
- Keeps `layer_norm`, `dropout`, and `classifier` trainable
- Reports trainable vs total parameters

### 4. Added debug function (`backend/lightweight_vsr/dataset.py`)

**New `read_frames_uniform` function:**
- Extracts frames uniformly from video files
- Supports custom frame count and size
- Returns normalized tensors for debugging
- Used in step 5 of the test plan

### 5. Created test automation scripts

**`run_real_test.py`:**
- Automates all 5 test steps
- Checks data, runs training, tests inference
- Provides comprehensive error reporting
- Includes confidence threshold testing

**`test_api_manual.py`:**
- Helper script for manual API testing
- Finds test videos automatically
- Tests server connectivity and endpoints

## Test Plan Implementation

The implementation follows the exact 5-step test plan:

1. **Data Check**: Validates manifest.csv and counts phrases
2. **Quick Training**: Runs frozen encoder training with specified parameters
3. **Smoke Test**: Tests inference with automatic confidence threshold adjustment
4. **API Test**: Provides instructions and helper script for manual testing
5. **Frame Debug**: Extracts and saves debug frames for visual inspection

## Usage

### Automated Test Run
```bash
python run_real_test.py
```

### Manual API Testing
```bash
# Terminal 1: Start server
export VSR_IMPL=lightweight
uvicorn backend.api.app:app --reload

# Terminal 2: Test API
python test_api_manual.py path/to/video.mp4
```

### Manual Training
```bash
python -m backend.lightweight_vsr.train \
  --manifest data/manifest.csv \
  --config configs/phrases26.yaml \
  --out_dir artifacts/vsr_fasthead_v1 \
  --epochs 8 --batch_size 32 --frames 16 --size 96 \
  --min_per_phrase 8 --max_per_phrase 40 \
  --freeze_encoder
```

### Manual Inference Testing
```python
from backend.lightweight_vsr.infer import predict_phrase

# Test with default threshold
result = predict_phrase('video.mp4', art_dir='artifacts/vsr_fasthead_v1')

# Test with lower threshold if needed
result = predict_phrase('video.mp4', art_dir='artifacts/vsr_fasthead_v1', confidence_threshold=0.55)
```

## Key Features

- **Frozen Encoder Training**: Only trains the classifier head while keeping the visual encoder frozen
- **Flexible Model Loading**: Supports both checkpoint (.pt/.ckpt) and TorchScript (.ts) formats
- **Confidence Threshold Adjustment**: Easy to test different thresholds for better predictions
- **Comprehensive Testing**: Automated pipeline with error handling and debugging tools
- **Production Ready**: API endpoint ready for real-world testing

## Expected Outcomes

- Fast training (8 epochs) due to frozen encoder
- Reduced overfitting with fewer trainable parameters
- Quick iteration and testing cycle
- Ready for real-world deployment and testing
