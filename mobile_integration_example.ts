// mobile_integration_example.ts
// Example integration for Expo mobile app with speaking video functionality

import * as FileSystem from 'expo-file-system';

const API_URL = 'http://YOUR_SERVER_IP:8000'; // Replace with your server IP

/**
 * Create a speaking video from a recorded video file
 * @param localUri - Local URI of the recorded video
 * @param phrase - Optional phrase to speak (if omitted, server will predict)
 * @returns Promise<string> - Local URI of the speaking video MP4
 */
async function makeSpeakingVideo(localUri: string, phrase?: string): Promise<string> {
  const form = new FormData();
  form.append("file", { 
    uri: localUri, 
    name: "clip.mp4", 
    type: "video/mp4" 
  } as any);
  
  if (phrase) {
    form.append("phrase", phrase);
  }
  
  const res = await fetch(`${API_URL}/speak_video`, { 
    method: "POST", 
    body: form as any 
  });
  
  if (!res.ok) {
    throw new Error(`speak_video failed: ${res.status} - ${await res.text()}`);
  }
  
  // Get prediction info from headers
  const predictedPhrase = res.headers.get('X-Phrase');
  const confidence = res.headers.get('X-Pred-Confidence');
  
  console.log(`Speaking video created - Phrase: "${predictedPhrase}", Confidence: ${confidence}`);
  
  const blob = await res.blob();

  // Convert blob to base64 and save locally
  const base64 = await new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve((reader.result as string).split(",")[1]);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
  
  const outPath = `${FileSystem.cacheDirectory}spoken_${Date.now()}.mp4`;
  await FileSystem.writeAsStringAsync(outPath, base64, { 
    encoding: FileSystem.EncodingType.Base64 
  });
  
  return outPath;
}

/**
 * Complete workflow: Record → Predict → Create Speaking Video → Play
 */
async function recordAndCreateSpeakingVideo() {
  try {
    // 1. Record video (your existing recording logic)
    const recordingUri = await recordVideo(); // Your existing function
    
    // 2. Get prediction from server
    const prediction = await getPrediction(recordingUri); // Your existing function
    console.log(`Predicted phrase: ${prediction.phrase} (${prediction.confidence})`);
    
    // 3. Create speaking video
    const speakingVideoUri = await makeSpeakingVideo(recordingUri, prediction.phrase);
    
    // 4. Play the speaking video
    await playVideo(speakingVideoUri); // Your existing video player
    
    console.log('Speaking video workflow completed successfully!');
    
  } catch (error) {
    console.error('Speaking video workflow failed:', error);
  }
}

/**
 * Alternative: Let server predict automatically
 */
async function recordAndAutoSpeak() {
  try {
    // 1. Record video
    const recordingUri = await recordVideo();
    
    // 2. Create speaking video (server will predict automatically)
    const speakingVideoUri = await makeSpeakingVideo(recordingUri);
    
    // 3. Play the speaking video
    await playVideo(speakingVideoUri);
    
    console.log('Auto-speaking video workflow completed!');
    
  } catch (error) {
    console.error('Auto-speaking video workflow failed:', error);
  }
}

// Example usage in your React Native component:
/*
import { Video } from 'expo-av';

export function LipreadingScreen() {
  const [speakingVideoUri, setSpeakingVideoUri] = useState<string | null>(null);
  
  const handleRecordAndSpeak = async () => {
    try {
      const recordingUri = await recordVideo();
      const speakingUri = await makeSpeakingVideo(recordingUri);
      setSpeakingVideoUri(speakingUri);
    } catch (error) {
      console.error('Failed to create speaking video:', error);
    }
  };
  
  return (
    <View>
      <Button title="Record & Create Speaking Video" onPress={handleRecordAndSpeak} />
      
      {speakingVideoUri && (
        <Video
          source={{ uri: speakingVideoUri }}
          style={{ width: 300, height: 200 }}
          useNativeControls
          resizeMode="contain"
          shouldPlay
        />
      )}
    </View>
  );
}
*/

// Placeholder functions (replace with your existing implementations)
async function recordVideo(): Promise<string> {
  // Your existing video recording logic
  throw new Error('Implement your video recording logic');
}

async function getPrediction(videoUri: string): Promise<{phrase: string, confidence: number}> {
  // Your existing prediction logic
  throw new Error('Implement your prediction logic');
}

async function playVideo(videoUri: string): Promise<void> {
  // Your existing video playback logic
  throw new Error('Implement your video playback logic');
}

export { makeSpeakingVideo, recordAndCreateSpeakingVideo, recordAndAutoSpeak };
