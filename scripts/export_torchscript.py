import torch
import json
import os
import sys
from pathlib import Path

# === CRITICAL: UPDATE THESE IMPORTS BASED ON YOUR ACTUAL MODEL LOCATION ===
# Add the current directory and backend directory to Python path
sys.path.insert(0, os.getcwd())
sys.path.insert(0, os.path.join(os.getcwd(), 'backend'))
sys.path.insert(0, os.path.join(os.getcwd(), 'backend', 'lightweight_vsr'))

# Import the Mobile3DTiny model
from model import Mobile3DTiny

# === CONFIGURATION (UPDATE THESE PATHS AS NEEDED) ===
# Available checkpoint options (uncomment the one you want to use):
# CKPT_PATH = "checkpoints/perfect_10_training/best_perfect_10_model.pth"  # Perfect 10 model
# CKPT_PATH = "checkpoints/lipnet_perfect10_fresh/best_model.pth"          # Fresh LipNet model
# CKPT_PATH = "checkpoints/focused_13_training/best_focused_model.pth"     # Focused 13 model
CKPT_PATH = "checkpoints/perfect_10_training/best_perfect_10_model.pth"    # Default: Perfect 10

LABELS_PATH = "labels.json"           # Already created
OUT_PATH = "server/weights/mobile3dtiny_10phrases.ts"

# === MODEL INPUT CONFIGURATION (VERIFIED FROM MODEL.PY) ===
# Mobile3DTiny expects: (batch_size, channels, frames, height, width)
# Based on line 169 in backend/lightweight_vsr/model.py: (2, 1, 32, 96, 96)
INPUT_SHAPE = (1, 1, 32, 96, 96)  # (batch=1, channels=1, frames=32, height=96, width=96)

def export_model():
    print("🚀 Starting TorchScript export process...")
    print(f"📍 Working directory: {os.getcwd()}")
    
    # Verify all required files exist
    if not os.path.exists(LABELS_PATH):
        raise FileNotFoundError(f"Labels file not found: {LABELS_PATH}")
    if not os.path.exists(CKPT_PATH):
        raise FileNotFoundError(f"Checkpoint file not found: {CKPT_PATH}")
    
    # Load labels to determine number of classes
    print(f"📋 Loading labels from {LABELS_PATH}")
    with open(LABELS_PATH) as f:
        labels = json.load(f)
    num_classes = len(labels)
    print(f"📊 Found {num_classes} classes: {labels}")
    
    # Initialize model architecture
    print(f"🏗️  Initializing model with {num_classes} classes")
    try:
        model = Mobile3DTiny(num_classes=num_classes)  # TODO: UPDATE CLASS NAME IF DIFFERENT
    except Exception as e:
        print(f"❌ Failed to initialize model. Check your import path and class name.")
        print(f"Error: {e}")
        raise
    
    # Load checkpoint
    print(f"📥 Loading checkpoint from {CKPT_PATH}")
    try:
        state_dict = torch.load(CKPT_PATH, map_location="cpu")
        print(f"✅ Checkpoint loaded successfully")
        print(f"📊 Checkpoint keys: {list(state_dict.keys())[:5]}...")  # Show first 5 keys
    except Exception as e:
        print(f"❌ Failed to load checkpoint: {e}")
        raise
    
    # Handle different checkpoint formats
    original_keys = list(state_dict.keys())
    if "state_dict" in state_dict:
        print("🔧 Detected PyTorch Lightning checkpoint format")
        state_dict = state_dict["state_dict"]
        # Remove common prefixes
        prefixes_to_remove = ["model.", "net.", "backbone."]
        for prefix in prefixes_to_remove:
            if any(k.startswith(prefix) for k in state_dict.keys()):
                print(f"🔧 Removing '{prefix}' prefix from keys")
                state_dict = {k.replace(prefix, ""): v for k, v in state_dict.items()}
                break
    elif "model_state_dict" in state_dict:
        print("🔧 Detected PyTorch training checkpoint format")
        state_dict = state_dict["model_state_dict"]
        # Remove common prefixes
        prefixes_to_remove = ["base_model.", "model.", "net.", "backbone."]
        for prefix in prefixes_to_remove:
            if any(k.startswith(prefix) for k in state_dict.keys()):
                print(f"🔧 Removing '{prefix}' prefix from keys")
                state_dict = {k.replace(prefix, ""): v for k, v in state_dict.items()}
                break
    else:
        print("🔧 Detected plain PyTorch checkpoint format")
    
    print(f"📊 Model state dict keys: {list(state_dict.keys())[:5]}...")
    
    # Load weights into model
    print("⚡ Loading weights into model")
    try:
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
        if missing_keys:
            print(f"⚠️  Missing keys: {missing_keys[:3]}...")  # Show first 3
        if unexpected_keys:
            print(f"⚠️  Unexpected keys: {unexpected_keys[:3]}...")  # Show first 3
        print("✅ Weights loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load weights: {e}")
        print("💡 This might indicate a mismatch between model architecture and checkpoint")
        raise
    
    model.eval()
    
    # Create dummy input for tracing
    print(f"🎯 Creating dummy input with shape: {INPUT_SHAPE}")
    example_input = torch.randn(*INPUT_SHAPE)
    
    # Test model with dummy input first
    print("🧪 Testing model with dummy input")
    try:
        with torch.no_grad():
            output = model(example_input)
        print(f"✅ Model test successful. Output shape: {output.shape}")
        if output.shape[-1] != num_classes:
            print(f"⚠️  Warning: Output classes ({output.shape[-1]}) != expected classes ({num_classes})")
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        print("💡 Check your input dimensions and model architecture")
        raise
    
    # Trace the model
    print("🔍 Tracing model with TorchScript")
    try:
        traced_model = torch.jit.trace(model, example_input)
        print("✅ Model tracing successful")
    except Exception as e:
        print(f"❌ Model tracing failed: {e}")
        raise
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(OUT_PATH), exist_ok=True)
    
    # Save TorchScript model
    print(f"💾 Saving TorchScript model to {OUT_PATH}")
    try:
        traced_model.save(OUT_PATH)
        print("✅ Model saved successfully")
    except Exception as e:
        print(f"❌ Failed to save model: {e}")
        raise
    
    # Verify the saved model
    if os.path.exists(OUT_PATH):
        file_size = os.path.getsize(OUT_PATH) / (1024 * 1024)  # Size in MB
        print(f"\n🎉 TorchScript export completed successfully!")
        print(f"📁 File: {OUT_PATH}")
        print(f"📏 Size: {file_size:.2f} MB")
        print(f"🏷️  Classes: {num_classes} ICU phrases")
        print(f"📐 Input shape: {INPUT_SHAPE}")
        
        # Test loading the saved model
        print("\n🧪 Testing saved TorchScript model...")
        try:
            loaded_model = torch.jit.load(OUT_PATH)
            test_output = loaded_model(example_input)
            print(f"✅ Saved model loads and runs correctly. Output shape: {test_output.shape}")
        except Exception as e:
            print(f"⚠️  Warning: Saved model test failed: {e}")
    else:
        raise FileNotFoundError("Model file was not created")

if __name__ == "__main__":
    try:
        export_model()
    except Exception as e:
        print(f"\n❌ Export failed: {str(e)}")
        print("\n🔍 Troubleshooting checklist:")
        print("   1. ✅ Checkpoint file exists and is accessible")
        print("   2. ❓ Model import path is correct (UPDATE THE IMPORT STATEMENT)")
        print("   3. ❓ Model class name matches your architecture")
        print("   4. ❓ Input dimensions match your model's expected format")
        print("   5. ❓ PyTorch version compatibility")
        print("   6. ❓ Model architecture matches the checkpoint")
        print(f"\n📍 Current working directory: {os.getcwd()}")
        print(f"📁 Files in current directory: {os.listdir('.')}")
        raise
