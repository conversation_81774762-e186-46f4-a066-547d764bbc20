#!/usr/bin/env python3
"""
Test script for the exported TorchScript model
Verifies that the model loads and produces reasonable predictions
"""

import torch
import json
import numpy as np
import os

def test_torchscript_model():
    print("🧪 Testing TorchScript Model")
    print("=" * 50)
    
    # Paths
    model_path = "server/weights/mobile3dtiny_10phrases.ts"
    labels_path = "labels.json"
    
    # Check if files exist
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return False
    
    if not os.path.exists(labels_path):
        print(f"❌ Labels file not found: {labels_path}")
        return False
    
    # Load labels
    print(f"📋 Loading labels from {labels_path}")
    with open(labels_path, 'r') as f:
        labels = json.load(f)
    print(f"📊 Found {len(labels)} classes: {labels}")
    
    # Load TorchScript model
    print(f"📥 Loading TorchScript model from {model_path}")
    try:
        model = torch.jit.load(model_path, map_location='cpu')
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return False
    
    # Test with dummy input
    print("🎯 Testing with dummy input...")
    input_shape = (1, 1, 32, 96, 96)  # (batch, channels, frames, height, width)
    dummy_input = torch.randn(*input_shape)
    
    try:
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✅ Model inference successful")
        print(f"📐 Input shape: {dummy_input.shape}")
        print(f"📐 Output shape: {output.shape}")
        
        # Apply softmax to get probabilities
        probabilities = torch.softmax(output, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities[0, predicted_class].item()
        
        print(f"🎯 Predicted class index: {predicted_class}")
        print(f"🏷️  Predicted phrase: '{labels[predicted_class]}'")
        print(f"📊 Confidence: {confidence:.3f}")
        
        # Show top 3 predictions
        top_probs, top_indices = torch.topk(probabilities[0], 3)
        print("\n🏆 Top 3 predictions:")
        for i, (prob, idx) in enumerate(zip(top_probs, top_indices)):
            print(f"  {i+1}. {labels[idx.item()]}: {prob.item():.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model inference failed: {e}")
        return False

def test_multiple_inputs():
    """Test with multiple random inputs to verify consistency"""
    print("\n🔄 Testing with multiple random inputs...")
    
    model_path = "server/weights/mobile3dtiny_10phrases.ts"
    labels_path = "labels.json"
    
    # Load model and labels
    model = torch.jit.load(model_path, map_location='cpu')
    model.eval()
    
    with open(labels_path, 'r') as f:
        labels = json.load(f)
    
    # Test with 5 different random inputs
    predictions = []
    for i in range(5):
        dummy_input = torch.randn(1, 1, 32, 96, 96)
        
        with torch.no_grad():
            output = model(dummy_input)
            probabilities = torch.softmax(output, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0, predicted_class].item()
            
            predictions.append({
                'phrase': labels[predicted_class],
                'confidence': confidence,
                'class_idx': predicted_class
            })
            
            print(f"  Test {i+1}: '{labels[predicted_class]}' (confidence: {confidence:.3f})")
    
    # Check for reasonable distribution
    unique_predictions = len(set(p['phrase'] for p in predictions))
    print(f"\n📊 Prediction diversity: {unique_predictions}/5 unique predictions")
    
    if unique_predictions >= 2:
        print("✅ Model shows good prediction diversity")
    else:
        print("⚠️  Model may be overfitting to one class")
    
    return predictions

def benchmark_inference_speed():
    """Benchmark inference speed"""
    print("\n⚡ Benchmarking inference speed...")
    
    model_path = "server/weights/mobile3dtiny_10phrases.ts"
    model = torch.jit.load(model_path, map_location='cpu')
    model.eval()
    
    # Warm up
    dummy_input = torch.randn(1, 1, 32, 96, 96)
    for _ in range(5):
        with torch.no_grad():
            _ = model(dummy_input)
    
    # Benchmark
    import time
    num_runs = 50
    start_time = time.time()
    
    for _ in range(num_runs):
        with torch.no_grad():
            _ = model(dummy_input)
    
    end_time = time.time()
    avg_time = (end_time - start_time) / num_runs
    
    print(f"📊 Average inference time: {avg_time*1000:.2f}ms")
    print(f"🚀 Throughput: {1/avg_time:.1f} inferences/second")
    
    if avg_time < 0.1:  # Less than 100ms
        print("✅ Excellent inference speed for real-time use")
    elif avg_time < 0.5:  # Less than 500ms
        print("✅ Good inference speed for mobile use")
    else:
        print("⚠️  Inference speed may be too slow for real-time use")
    
    return avg_time

if __name__ == "__main__":
    print("🚀 TorchScript Model Testing Suite")
    print("=" * 60)
    
    # Test basic functionality
    success = test_torchscript_model()
    
    if success:
        # Test multiple inputs
        predictions = test_multiple_inputs()
        
        # Benchmark speed
        avg_time = benchmark_inference_speed()
        
        print("\n🎉 All tests completed successfully!")
        print("=" * 60)
        print("✅ TorchScript model is ready for integration with your backend")
        print(f"📁 Model file: server/weights/mobile3dtiny_10phrases.ts")
        print(f"📏 Model size: {os.path.getsize('server/weights/mobile3dtiny_10phrases.ts') / (1024*1024):.2f} MB")
        print(f"⚡ Inference speed: {avg_time*1000:.2f}ms")
        print(f"🏷️  Classes: 10 ICU phrases")
        
        print("\n💡 Next steps:")
        print("1. Update your backend to load this TorchScript model")
        print("2. Replace the dummy model with real predictions")
        print("3. Test with actual video inputs from your mobile app")
        
    else:
        print("\n❌ Tests failed. Please check the model export process.")
