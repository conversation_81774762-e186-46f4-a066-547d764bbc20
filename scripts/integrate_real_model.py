#!/usr/bin/env python3
"""
Integration script to update the backend with the real TorchScript model
Replaces the dummy model with your trained SageMaker model
"""

import os
import shutil
import json

def backup_current_backend():
    """Create a backup of the current simple_backend.py"""
    print("📦 Creating backup of current backend...")
    
    if os.path.exists("simple_backend.py"):
        backup_path = "simple_backend_backup.py"
        shutil.copy2("simple_backend.py", backup_path)
        print(f"✅ Backup created: {backup_path}")
        return True
    else:
        print("❌ simple_backend.py not found")
        return False

def create_real_model_backend():
    """Create an updated backend that uses the real TorchScript model"""
    
    backend_code = '''#!/usr/bin/env python3
"""
ICU Lipreading Backend with Real SageMaker-Trained Model
Serves TorchScript model for mobile app predictions
"""

import os
import sys
import tempfile
import time
import logging
import json
from pathlib import Path
from typing import Optional
from datetime import datetime

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="ICU Lipreading API with Real Model", version="2.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Performance tracking
performance_stats = {
    "total_requests": 0,
    "successful_predictions": 0,
    "failed_predictions": 0,
    "average_processing_time": 0.0,
    "last_request_time": None,
    "uptime_start": datetime.now()
}

class RealVSRModel:
    """Real Visual Speech Recognition model using TorchScript"""
    
    def __init__(self, model_path: str, labels_path: str):
        self.model_path = model_path
        self.labels_path = labels_path
        self.model = None
        self.labels = []
        self.device = torch.device('cpu')  # Use CPU for compatibility
        
        self.load_model()
        self.load_labels()
    
    def load_model(self):
        """Load the TorchScript model"""
        try:
            logger.info(f"Loading TorchScript model from {self.model_path}")
            self.model = torch.jit.load(self.model_path, map_location=self.device)
            self.model.eval()
            logger.info("✅ Real model loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            raise
    
    def load_labels(self):
        """Load class labels"""
        try:
            with open(self.labels_path, 'r') as f:
                self.labels = json.load(f)
            logger.info(f"✅ Loaded {len(self.labels)} class labels")
        except Exception as e:
            logger.error(f"❌ Failed to load labels: {e}")
            raise
    
    def preprocess_video(self, video_path: str) -> torch.Tensor:
        """
        Preprocess video for model input
        Expected format: (1, 1, 32, 96, 96) - (batch, channels, frames, height, width)
        """
        try:
            # Open video
            cap = cv2.VideoCapture(video_path)
            frames = []
            
            # Read all frames
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Convert to grayscale
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Resize to 96x96 (model expected size)
                resized = cv2.resize(gray, (96, 96))
                
                # Normalize to [0, 1]
                normalized = resized.astype(np.float32) / 255.0
                
                frames.append(normalized)
            
            cap.release()
            
            # Ensure we have exactly 32 frames (model requirement)
            target_frames = 32
            if len(frames) > target_frames:
                # Sample frames evenly
                indices = np.linspace(0, len(frames) - 1, target_frames, dtype=int)
                frames = [frames[i] for i in indices]
            elif len(frames) < target_frames:
                # Repeat last frame to reach target
                while len(frames) < target_frames:
                    frames.append(frames[-1])
            
            # Convert to tensor: (1, 1, 32, 96, 96)
            video_tensor = torch.tensor(frames, dtype=torch.float32)
            video_tensor = video_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            
            logger.info(f"📹 Preprocessed video: {len(frames)} frames -> {video_tensor.shape}")
            return video_tensor
            
        except Exception as e:
            logger.error(f"❌ Video preprocessing failed: {e}")
            raise
    
    def predict(self, video_path: str) -> dict:
        """Make prediction on video"""
        try:
            # Preprocess video
            video_tensor = self.preprocess_video(video_path)
            
            # Run inference
            with torch.no_grad():
                start_time = time.time()
                logits = self.model(video_tensor)
                inference_time = time.time() - start_time
                
                # Apply softmax to get probabilities
                probabilities = F.softmax(logits, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
                confidence = probabilities[0, predicted_class].item()
                
                # Get predicted phrase
                predicted_phrase = self.labels[predicted_class]
                
                logger.info(f"🎯 Prediction: '{predicted_phrase}' (confidence: {confidence:.3f}, time: {inference_time*1000:.1f}ms)")
                
                return {
                    "prediction": predicted_phrase,
                    "confidence": confidence,
                    "model_type": "mobile3dtiny_torchscript",
                    "inference_time": inference_time,
                    "class_index": predicted_class
                }
                
        except Exception as e:
            logger.error(f"❌ Prediction failed: {e}")
            raise

# Initialize the real model
MODEL_PATH = "server/weights/mobile3dtiny_10phrases.ts"
LABELS_PATH = "labels.json"

try:
    if os.path.exists(MODEL_PATH) and os.path.exists(LABELS_PATH):
        vsr_model = RealVSRModel(MODEL_PATH, LABELS_PATH)
        logger.info("🚀 Real VSR model initialized successfully")
    else:
        logger.error(f"❌ Model files not found: {MODEL_PATH}, {LABELS_PATH}")
        vsr_model = None
except Exception as e:
    logger.error(f"❌ Failed to initialize real model: {e}")
    vsr_model = None

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": vsr_model is not None,
        "model_type": "mobile3dtiny_torchscript" if vsr_model else "none",
        "vsr_impl": "real_model",
        "classes": len(vsr_model.labels) if vsr_model else 0
    }

@app.post("/predict")
async def predict_video(file: UploadFile = File(...)):
    """Predict phrase from uploaded video using real model"""
    global performance_stats
    
    start_time = time.time()
    performance_stats["total_requests"] += 1
    performance_stats["last_request_time"] = datetime.now()
    
    logger.info(f"🎥 Received prediction request - File: {file.filename}, Size: {file.size if hasattr(file, 'size') else 'unknown'}")
    
    if not vsr_model:
        performance_stats["failed_predictions"] += 1
        raise HTTPException(status_code=500, detail="Real model not loaded")
    
    # Validate file type
    if file.content_type and not (file.content_type.startswith('video/') or 
                                  file.content_type.startswith('application/octet-stream')):
        performance_stats["failed_predictions"] += 1
        logger.warning(f"❌ Invalid content type: {file.content_type}")
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Also check file extension as fallback
    if file.filename:
        valid_extensions = ['.mp4', '.webm', '.mov', '.avi']
        if not any(file.filename.lower().endswith(ext) for ext in valid_extensions):
            performance_stats["failed_predictions"] += 1
            logger.warning(f"❌ Invalid file extension: {file.filename}")
            raise HTTPException(status_code=400, detail="File must be a video (mp4, webm, mov, or avi)")
    
    try:
        # Save uploaded file temporarily
        upload_start = time.time()
        with tempfile.NamedTemporaryFile(delete=False, suffix='.webm') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        upload_time = time.time() - upload_start
        file_size = len(content)
        logger.info(f"📁 File uploaded - Size: {file_size} bytes, Upload time: {upload_time:.2f}s")
        
        # Run real model prediction
        prediction_start = time.time()
        result = vsr_model.predict(tmp_file_path)
        prediction_time = time.time() - prediction_start
        
        # Clean up
        os.unlink(tmp_file_path)
        
        # Update performance stats
        total_time = time.time() - start_time
        performance_stats["successful_predictions"] += 1
        performance_stats["average_processing_time"] = (
            (performance_stats["average_processing_time"] * (performance_stats["successful_predictions"] - 1) + total_time) /
            performance_stats["successful_predictions"]
        )
        
        logger.info(f"✅ Real prediction successful - Phrase: '{result['prediction']}', Confidence: {result['confidence']:.3f}, Total time: {total_time:.2f}s")
        
        return {
            "success": True,
            "prediction": result["prediction"],
            "confidence": result["confidence"],
            "model_type": result["model_type"],
            "processing_time": total_time,
            "file_size": file_size,
            "inference_time": result["inference_time"],
            "class_index": result["class_index"]
        }
        
    except Exception as e:
        # Clean up on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass
        
        performance_stats["failed_predictions"] += 1
        error_time = time.time() - start_time
        logger.error(f"❌ Real prediction failed after {error_time:.2f}s - Error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/phrases")
async def get_supported_phrases():
    """Get list of supported phrases from real model"""
    if vsr_model:
        return {"phrases": vsr_model.labels}
    else:
        return {"phrases": []}

@app.get("/stats")
async def get_performance_stats():
    """Get performance statistics for monitoring"""
    uptime = datetime.now() - performance_stats["uptime_start"]
    
    return {
        "performance": performance_stats,
        "uptime_seconds": uptime.total_seconds(),
        "uptime_formatted": str(uptime),
        "requests_per_minute": performance_stats["total_requests"] / max(uptime.total_seconds() / 60, 1),
        "success_rate": (
            performance_stats["successful_predictions"] / max(performance_stats["total_requests"], 1) * 100
        ),
        "model_status": {
            "loaded": vsr_model is not None,
            "type": "mobile3dtiny_torchscript" if vsr_model else "none",
            "classes": len(vsr_model.labels) if vsr_model else 0
        }
    }

@app.get("/test-connection")
async def test_connection():
    """Simple endpoint for connection testing"""
    return {
        "status": "connected",
        "timestamp": datetime.now().isoformat(),
        "server": "ICU Lipreading API with Real Model",
        "ready": vsr_model is not None,
        "model_type": "mobile3dtiny_torchscript" if vsr_model else "none"
    }

if __name__ == "__main__":
    print("🚀 Starting ICU Lipreading Backend with Real Model")
    print(f"📁 Model: {MODEL_PATH}")
    print(f"🏷️  Labels: {LABELS_PATH}")
    print(f"🎯 Model loaded: {vsr_model is not None}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
'''
    
    # Write the new backend
    with open("real_model_backend.py", "w") as f:
        f.write(backend_code)
    
    print("✅ Created real_model_backend.py")
    return True

def main():
    print("🚀 ICU Lipreading Backend Integration")
    print("=" * 50)
    
    # Check prerequisites
    model_path = "server/weights/mobile3dtiny_10phrases.ts"
    labels_path = "labels.json"
    
    if not os.path.exists(model_path):
        print(f"❌ TorchScript model not found: {model_path}")
        print("Please run the export script first: python scripts/export_torchscript.py")
        return False
    
    if not os.path.exists(labels_path):
        print(f"❌ Labels file not found: {labels_path}")
        return False
    
    print(f"✅ TorchScript model found: {model_path}")
    print(f"✅ Labels file found: {labels_path}")
    
    # Create backup
    backup_success = backup_current_backend()
    
    # Create new backend
    if create_real_model_backend():
        print("\n🎉 Integration completed successfully!")
        print("=" * 50)
        print("📁 Files created:")
        print("  - real_model_backend.py (new backend with real model)")
        if backup_success:
            print("  - simple_backend_backup.py (backup of original)")
        
        print("\n💡 Next steps:")
        print("1. Test the new backend:")
        print("   python real_model_backend.py")
        print("2. Update your mobile app to use real predictions")
        print("3. Test with actual video recordings")
        print("4. If everything works, replace simple_backend.py:")
        print("   mv real_model_backend.py simple_backend.py")
        
        return True
    else:
        print("❌ Integration failed")
        return False

if __name__ == "__main__":
    main()
