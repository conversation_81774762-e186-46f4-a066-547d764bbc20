#!/usr/bin/env python3
"""
Dataset processing pipeline for hybrid core-word ICU lipreading system
Processes raw webm videos from desktop folder into training-ready format

Core Words: pillow, help, glasses, phone, doctor (5 anchor words for >95% accuracy)
"""

import os
import sys
import csv
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
import torch
from tqdm import tqdm
import re
import random
import imageio

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.mouth_detector import MouthDetector

class CoreWordAugmentation:
    """Aggressive data augmentation for core word training"""

    def __init__(self, target_samples_per_word: int = 400):
        self.target_samples = target_samples_per_word
        self.augmentation_params = {
            'temporal_shift_range': 3,      # ±3 frame shifts
            'speed_range': (0.9, 1.1),     # Speed variations
            'scale_range': 0.1,             # ±10% scaling
            'rotation_range': 5,            # ±5° rotation
            'translation_range': 10,        # ±10 pixel translation
            'brightness_range': 0.15,       # ±15% brightness
            'contrast_range': 0.15,         # ±15% contrast
            'noise_std': 0.02               # Gaussian noise σ=0.02
        }

    def calculate_augmentation_factor(self, original_count: int) -> int:
        """Calculate how many augmented versions needed per original sample"""
        if original_count >= self.target_samples:
            return 1  # No augmentation needed

        factor = max(1, int(np.ceil(self.target_samples / original_count)))
        return min(factor, 5)  # Cap at 5x augmentation to prevent overfitting

    def temporal_augment(self, tensor: torch.Tensor) -> torch.Tensor:
        """Apply temporal augmentation (frame shifts, speed changes)"""
        frames, channels, height, width = tensor.shape

        # Random frame shift
        shift = random.randint(-self.augmentation_params['temporal_shift_range'],
                              self.augmentation_params['temporal_shift_range'])

        if shift != 0:
            if shift > 0:
                # Shift forward, pad beginning
                padded = torch.cat([tensor[:1].repeat(shift, 1, 1, 1), tensor[:-shift]], dim=0)
            else:
                # Shift backward, pad end
                padded = torch.cat([tensor[-shift:], tensor[-1:].repeat(-shift, 1, 1, 1)], dim=0)
            tensor = padded

        return tensor

    def spatial_augment(self, tensor: torch.Tensor) -> torch.Tensor:
        """Apply spatial augmentation (scaling, rotation, translation)"""
        frames, channels, height, width = tensor.shape
        augmented_frames = []

        # Random augmentation parameters
        scale = 1.0 + random.uniform(-self.augmentation_params['scale_range'],
                                    self.augmentation_params['scale_range'])
        angle = random.uniform(-self.augmentation_params['rotation_range'],
                              self.augmentation_params['rotation_range'])
        tx = random.randint(-self.augmentation_params['translation_range'],
                           self.augmentation_params['translation_range'])
        ty = random.randint(-self.augmentation_params['translation_range'],
                           self.augmentation_params['translation_range'])

        for frame_idx in range(frames):
            frame = tensor[frame_idx, 0].numpy()  # Remove channel dimension

            # Apply transformations
            if scale != 1.0:
                # Scale
                center = (height // 2, width // 2)
                M = cv2.getRotationMatrix2D(center, 0, scale)
                frame = cv2.warpAffine(frame, M, (width, height), borderMode=cv2.BORDER_REFLECT)

            if angle != 0:
                # Rotate
                center = (height // 2, width // 2)
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                frame = cv2.warpAffine(frame, M, (width, height), borderMode=cv2.BORDER_REFLECT)

            if tx != 0 or ty != 0:
                # Translate
                M = np.float32([[1, 0, tx], [0, 1, ty]])
                frame = cv2.warpAffine(frame, M, (width, height), borderMode=cv2.BORDER_REFLECT)

            # Convert back to tensor format
            frame_tensor = torch.from_numpy(frame).unsqueeze(0)  # Add channel dimension
            augmented_frames.append(frame_tensor)

        return torch.stack(augmented_frames, dim=0)

    def appearance_augment(self, tensor: torch.Tensor) -> torch.Tensor:
        """Apply appearance augmentation (brightness, contrast, noise)"""
        # Random parameters
        brightness_factor = 1.0 + random.uniform(-self.augmentation_params['brightness_range'],
                                                 self.augmentation_params['brightness_range'])
        contrast_factor = 1.0 + random.uniform(-self.augmentation_params['contrast_range'],
                                              self.augmentation_params['contrast_range'])

        # Apply brightness and contrast
        tensor = tensor * contrast_factor + (brightness_factor - 1.0)

        # Add Gaussian noise
        if self.augmentation_params['noise_std'] > 0:
            noise = torch.randn_like(tensor) * self.augmentation_params['noise_std']
            tensor = tensor + noise

        # Clamp to valid range [0, 1]
        tensor = torch.clamp(tensor, 0.0, 1.0)

        return tensor

    def augment_tensor(self, tensor: torch.Tensor, augmentation_type: str = 'all') -> torch.Tensor:
        """Apply augmentation to a tensor"""
        augmented = tensor.clone()

        if augmentation_type in ['all', 'temporal']:
            augmented = self.temporal_augment(augmented)

        if augmentation_type in ['all', 'spatial']:
            augmented = self.spatial_augment(augmented)

        if augmentation_type in ['all', 'appearance']:
            augmented = self.appearance_augment(augmented)

        return augmented

class CoreWordDatasetProcessor:
    """Process raw webm videos for core word classification training"""
    
    # Define the 5 core words for hybrid system
    CORE_WORDS = ["pillow", "help", "glasses", "phone", "doctor"]
    
    def __init__(self, 
                 source_dir: str = "/Users/<USER>/Desktop/top 5 dataset 30.8.25",
                 output_dir: str = "data/core_words",
                 processed_dir: str = "data/core_words/processed"):
        """
        Initialize dataset processor
        
        Args:
            source_dir: Path to raw webm videos
            output_dir: Output directory for processed dataset
            processed_dir: Directory for processed video files
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.processed_dir = Path(processed_dir)
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize video processor with core word settings
        self.video_processor = VideoProcessor(
            target_frames=16,  # Reduced for core words
            target_size=(96, 96),
            grayscale=True,
            use_mouth_detection=True,
            mouth_detection_fallback=True
        )
        
        # Initialize mouth detector
        try:
            self.mouth_detector = MouthDetector()
            print("✅ Mouth detector initialized successfully")
        except Exception as e:
            print(f"⚠️  Mouth detector initialization failed: {e}")
            print("Will use fallback center cropping")
            self.mouth_detector = None
        
        # Initialize augmentation engine
        self.augmentation = CoreWordAugmentation(target_samples_per_word=400)

        # Statistics tracking
        self.stats = {
            "total_videos": 0,
            "processed_videos": 0,
            "failed_videos": 0,
            "core_word_counts": {word: 0 for word in self.CORE_WORDS},
            "augmented_counts": {word: 0 for word in self.CORE_WORDS},
            "processing_errors": [],
            "augmentation_factors": {word: 1 for word in self.CORE_WORDS}
        }
    
    def extract_core_word_from_filename(self, filename: str) -> Optional[str]:
        """
        Extract core word from video filename

        Args:
            filename: Video filename

        Returns:
            Core word if found, None otherwise
        """
        filename_lower = filename.lower()

        # Check for each core word in filename
        for word in self.CORE_WORDS:
            if word in filename_lower:
                return word

        # Additional pattern matching for variations
        word_patterns = {
            "pillow": ["pillow", "cushion"],
            "help": ["help", "assist", "aid"],
            "glasses": ["glasses", "spectacles", "eyeglasses"],
            "phone": ["phone", "telephone", "call"],
            "doctor": ["doctor", "physician", "dr"]
        }

        for core_word, patterns in word_patterns.items():
            for pattern in patterns:
                if pattern in filename_lower:
                    return core_word

        return None

    def extract_speaker_id(self, filename: str) -> str:
        """
        Extract speaker ID from filename for speaker-wise splits

        Args:
            filename: Video filename (format: word__useruser01__age__gender__ethnicity__timestamp.webm)

        Returns:
            Speaker ID for grouping
        """
        try:
            # Parse filename format: word__useruser01__18to39__female__caucasian__timestamp.webm
            parts = filename.split('__')
            if len(parts) >= 5:
                user_id = parts[1]  # useruser01
                age_group = parts[2]  # 18to39
                gender = parts[3]  # female
                ethnicity = parts[4]  # caucasian

                # Create speaker ID combining user, demographics
                speaker_id = f"{user_id}_{age_group}_{gender}_{ethnicity}"
                return speaker_id
            else:
                # Fallback: use first part as speaker ID
                return parts[1] if len(parts) > 1 else "unknown"
        except Exception:
            return "unknown"
    
    def process_single_video(self, video_path: Path) -> Optional[Dict]:
        """
        Process a single video file with robust webm handling

        Args:
            video_path: Path to video file

        Returns:
            Processing result dictionary or None if failed
        """
        try:
            # Extract core word from filename
            core_word = self.extract_core_word_from_filename(video_path.name)
            if not core_word:
                print(f"⚠️  No core word found in filename: {video_path.name}")
                return None

            # Process video with robust webm handling
            print(f"Processing {video_path.name} -> {core_word}")

            # Use custom video processing for webm files
            processed_tensor = self.process_webm_video(video_path)

            if processed_tensor is None:
                raise ValueError("No frames extracted from video")

            # Generate output filename
            output_filename = f"{core_word}_{video_path.stem}_processed.pt"
            output_path = self.processed_dir / output_filename

            # Save processed tensor
            torch.save(processed_tensor, output_path)

            # Update statistics
            self.stats["core_word_counts"][core_word] += 1
            self.stats["processed_videos"] += 1

            # Return metadata
            return {
                "original_path": str(video_path),
                "processed_path": str(output_path),
                "core_word": core_word,
                "filename": output_filename,
                "tensor_shape": list(processed_tensor.shape),
                "processing_status": "success"
            }

        except Exception as e:
            error_msg = f"Failed to process {video_path.name}: {str(e)}"
            print(f"❌ {error_msg}")
            self.stats["processing_errors"].append(error_msg)
            self.stats["failed_videos"] += 1
            return None

    def process_webm_video(self, video_path: Path) -> Optional[torch.Tensor]:
        """
        Robust webm video processing with mouth detection

        Args:
            video_path: Path to webm video file

        Returns:
            Processed tensor of shape (16, 1, 96, 96) or None if failed
        """
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                return None

            # Read all frames first (webm frame count is unreliable)
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)

            cap.release()

            if len(frames) == 0:
                return None

            # Sample 16 frames uniformly
            target_frames = 16
            if len(frames) <= target_frames:
                # If we have fewer frames, repeat the last frame
                selected_frames = frames[:]
                while len(selected_frames) < target_frames:
                    selected_frames.append(frames[-1])
            else:
                # Sample uniformly
                indices = np.linspace(0, len(frames) - 1, target_frames, dtype=int)
                selected_frames = [frames[i] for i in indices]

            # Process each frame
            processed_frames = []
            for frame in selected_frames:
                # Convert to grayscale
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                # Try mouth detection first
                mouth_region = self.extract_mouth_region(gray)
                if mouth_region is not None:
                    processed_frame = mouth_region
                else:
                    # Fallback: center crop focusing on upper-middle region
                    h, w = gray.shape
                    # Focus on top 60% of the frame (where mouth typically is)
                    crop_h = int(h * 0.6)
                    start_y = int(h * 0.2)  # Start from 20% down
                    end_y = start_y + crop_h

                    # Center crop horizontally
                    crop_w = min(crop_h, w)  # Square crop
                    start_x = (w - crop_w) // 2
                    end_x = start_x + crop_w

                    cropped = gray[start_y:end_y, start_x:end_x]
                    processed_frame = cropped

                # Resize to target size
                resized = cv2.resize(processed_frame, (96, 96), interpolation=cv2.INTER_AREA)

                # Normalize to [0, 1]
                normalized = resized.astype(np.float32) / 255.0

                processed_frames.append(normalized)

            # Convert to tensor format: (frames, channels, height, width)
            frames_array = np.array(processed_frames)  # (16, 96, 96)
            frames_array = np.expand_dims(frames_array, axis=1)  # (16, 1, 96, 96)

            return torch.from_numpy(frames_array)

        except Exception as e:
            print(f"Error processing webm video {video_path}: {e}")
            return None

    def extract_mouth_region(self, gray_frame: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract mouth region using mouth detector

        Args:
            gray_frame: Grayscale frame

        Returns:
            Mouth region or None if detection failed
        """
        try:
            if self.mouth_detector is None:
                return None

            # Convert to RGB for mouth detector (it expects RGB)
            rgb_frame = cv2.cvtColor(cv2.cvtColor(gray_frame, cv2.COLOR_GRAY2BGR), cv2.COLOR_BGR2RGB)

            # Detect mouth region
            mouth_bbox = self.mouth_detector.detect_mouth(rgb_frame)

            if mouth_bbox is not None:
                x, y, w, h = mouth_bbox

                # Add padding around mouth region
                padding = 20
                x = max(0, x - padding)
                y = max(0, y - padding)
                w = min(gray_frame.shape[1] - x, w + 2 * padding)
                h = min(gray_frame.shape[0] - y, h + 2 * padding)

                # Extract mouth region
                mouth_region = gray_frame[y:y+h, x:x+w]

                # Make it square by padding if needed
                if mouth_region.shape[0] != mouth_region.shape[1]:
                    max_dim = max(mouth_region.shape)
                    square_region = np.zeros((max_dim, max_dim), dtype=mouth_region.dtype)

                    # Center the mouth region in the square
                    start_y = (max_dim - mouth_region.shape[0]) // 2
                    start_x = (max_dim - mouth_region.shape[1]) // 2
                    square_region[start_y:start_y+mouth_region.shape[0],
                                start_x:start_x+mouth_region.shape[1]] = mouth_region

                    return square_region
                else:
                    return mouth_region

            return None

        except Exception:
            return None
    
    def scan_source_directory(self) -> List[Path]:
        """
        Scan source directory for video files
        
        Returns:
            List of video file paths
        """
        video_extensions = ['.webm', '.mp4', '.avi', '.mov']
        video_files = []
        
        if not self.source_dir.exists():
            print(f"❌ Source directory not found: {self.source_dir}")
            return []
        
        print(f"🔍 Scanning {self.source_dir} for video files...")
        
        for ext in video_extensions:
            files = list(self.source_dir.glob(f"*{ext}"))
            video_files.extend(files)
            print(f"Found {len(files)} {ext} files")
        
        print(f"📊 Total video files found: {len(video_files)}")
        return video_files
    
    def create_manifest(self, processed_results: List[Dict]) -> str:
        """
        Create training manifest CSV file
        
        Args:
            processed_results: List of processing results
            
        Returns:
            Path to created manifest file
        """
        manifest_path = self.output_dir / "manifest.csv"
        
        print(f"📝 Creating manifest file: {manifest_path}")
        
        with open(manifest_path, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Write header (use 'phrase' to match training script expectations)
            writer.writerow(['video_path', 'phrase', 'tensor_shape', 'filename'])
            
            # Write data rows
            for result in processed_results:
                if result:  # Skip failed processing results
                    writer.writerow([
                        result['processed_path'],
                        result['core_word'],  # This will be the 'phrase' column
                        str(result['tensor_shape']),
                        result['filename']
                    ])
        
        print(f"✅ Manifest created with {len(processed_results)} entries")
        return str(manifest_path)
    
    def create_speaker_wise_splits(self, processed_results: List[Dict]) -> Dict[str, List[Dict]]:
        """
        Create train/validation/test splits using speaker-wise splitting to prevent leakage

        Args:
            processed_results: List of processing results

        Returns:
            Dictionary with train/val/test splits
        """
        # Filter successful results
        valid_results = [r for r in processed_results if r is not None]

        # Group by speaker ID
        speaker_groups = {}
        for result in valid_results:
            filename = Path(result['original_path']).name
            speaker_id = self.extract_speaker_id(filename)

            if speaker_id not in speaker_groups:
                speaker_groups[speaker_id] = []
            speaker_groups[speaker_id].append(result)

        print(f"📊 Found {len(speaker_groups)} unique speakers")

        # Sort speakers by number of samples for balanced splitting
        sorted_speakers = sorted(speaker_groups.items(), key=lambda x: len(x[1]), reverse=True)

        # Initialize splits
        splits = {'train': [], 'val': [], 'test': []}
        split_sizes = {'train': 0, 'val': 0, 'test': 0}

        # Target proportions
        total_samples = len(valid_results)
        target_train = int(0.7 * total_samples)
        target_val = int(0.2 * total_samples)

        # Assign speakers to splits to balance data
        for speaker_id, speaker_data in sorted_speakers:
            # Choose split with smallest current size relative to target
            train_ratio = split_sizes['train'] / max(target_train, 1)
            val_ratio = split_sizes['val'] / max(target_val, 1)
            test_ratio = split_sizes['test'] / max(total_samples - target_train - target_val, 1)

            if train_ratio <= val_ratio and train_ratio <= test_ratio:
                splits['train'].extend(speaker_data)
                split_sizes['train'] += len(speaker_data)
            elif val_ratio <= test_ratio:
                splits['val'].extend(speaker_data)
                split_sizes['val'] += len(speaker_data)
            else:
                splits['test'].extend(speaker_data)
                split_sizes['test'] += len(speaker_data)

        # Print split statistics
        print(f"📊 Speaker-wise data splits created:")
        for split_name, split_data in splits.items():
            print(f"  {split_name}: {len(split_data)} samples")

            # Count per core word
            word_counts = {}
            for item in split_data:
                word = item['core_word']
                word_counts[word] = word_counts.get(word, 0) + 1
            print(f"    Word distribution: {word_counts}")

        return splits

    def apply_augmentation_to_training_set(self, train_data: List[Dict]) -> List[Dict]:
        """
        Apply aggressive augmentation to training set only to reach 400+ samples per word

        Args:
            train_data: Training split data

        Returns:
            Augmented training data
        """
        print("\n🔄 Applying data augmentation to training set...")

        # Group training data by core word
        word_groups = {}
        for item in train_data:
            word = item['core_word']
            if word not in word_groups:
                word_groups[word] = []
            word_groups[word].append(item)

        # Calculate augmentation factors
        for word in self.CORE_WORDS:
            original_count = len(word_groups.get(word, []))
            aug_factor = self.augmentation.calculate_augmentation_factor(original_count)
            self.stats['augmentation_factors'][word] = aug_factor
            print(f"  {word}: {original_count} original → {aug_factor}x augmentation")

        # Apply augmentation
        augmented_train_data = []

        for word, word_data in word_groups.items():
            aug_factor = self.stats['augmentation_factors'][word]

            # Keep original samples
            augmented_train_data.extend(word_data)

            # Generate augmented samples
            for original_item in tqdm(word_data, desc=f"Augmenting {word}"):
                for aug_idx in range(aug_factor - 1):  # -1 because we keep original
                    try:
                        # Load original tensor
                        original_tensor = torch.load(original_item['processed_path'])

                        # Apply augmentation
                        augmented_tensor = self.augmentation.augment_tensor(original_tensor)

                        # Generate new filename
                        original_filename = original_item['filename']
                        base_name = original_filename.replace('.pt', '')
                        aug_filename = f"{base_name}_aug{aug_idx+1}.pt"
                        aug_path = self.processed_dir / aug_filename

                        # Save augmented tensor
                        torch.save(augmented_tensor, aug_path)

                        # Create augmented item record
                        aug_item = original_item.copy()
                        aug_item['processed_path'] = str(aug_path)
                        aug_item['filename'] = aug_filename
                        aug_item['augmented'] = True
                        aug_item['augmentation_type'] = 'all'

                        augmented_train_data.append(aug_item)
                        self.stats['augmented_counts'][word] += 1

                    except Exception as e:
                        print(f"⚠️  Augmentation failed for {original_item['filename']}: {e}")

        print(f"✅ Augmentation completed:")
        for word in self.CORE_WORDS:
            original = len(word_groups.get(word, []))
            augmented = self.stats['augmented_counts'][word]
            total = original + augmented
            print(f"  {word}: {original} original + {augmented} augmented = {total} total")

        return augmented_train_data
    
    def save_processing_stats(self):
        """Save processing statistics to JSON file"""
        stats_path = self.output_dir / "processing_stats.json"

        with open(stats_path, 'w') as f:
            json.dump(self.stats, f, indent=2)

        print(f"📊 Processing statistics saved to {stats_path}")

    def save_debug_frames(self, splits: Dict[str, List[Dict]], samples_per_word: int = 3):
        """
        Save debug frames for ROI validation - ensure mouth visibility

        Args:
            splits: Data splits dictionary
            samples_per_word: Number of debug samples per core word
        """
        debug_dir = self.output_dir / "debug_frames"
        debug_dir.mkdir(exist_ok=True)

        print(f"\n🔍 Saving debug frames for ROI validation...")

        # Collect samples from validation set (original, non-augmented)
        val_data = splits['val']

        # Group by core word
        word_samples = {}
        for item in val_data:
            word = item['core_word']
            if word not in word_samples:
                word_samples[word] = []
            if not item.get('augmented', False):  # Only original samples
                word_samples[word].append(item)

        # Save debug frames for each core word
        for word in self.CORE_WORDS:
            if word not in word_samples:
                continue

            word_dir = debug_dir / word
            word_dir.mkdir(exist_ok=True)

            # Take first N samples
            samples = word_samples[word][:samples_per_word]

            for idx, sample in enumerate(samples):
                try:
                    # Load processed tensor
                    tensor = torch.load(sample['processed_path'])

                    # Save first, middle, and last frames
                    frames_to_save = [0, tensor.shape[0]//2, tensor.shape[0]-1]

                    for frame_idx in frames_to_save:
                        frame = tensor[frame_idx, 0].numpy()  # Remove channel dim
                        frame_uint8 = (frame * 255).astype(np.uint8)

                        # Save frame
                        frame_filename = f"{word}_sample{idx+1}_frame{frame_idx}.png"
                        frame_path = word_dir / frame_filename

                        cv2.imwrite(str(frame_path), frame_uint8)

                except Exception as e:
                    print(f"⚠️  Failed to save debug frames for {sample['filename']}: {e}")

        print(f"✅ Debug frames saved to {debug_dir}")
        print("   Please manually inspect frames to ensure mouth visibility in all samples")
    
    def process_dataset(self) -> bool:
        """
        Main processing pipeline
        
        Returns:
            True if processing successful, False otherwise
        """
        print("🚀 Starting core word dataset processing...")
        print(f"Source: {self.source_dir}")
        print(f"Output: {self.output_dir}")
        print(f"Target core words: {', '.join(self.CORE_WORDS)}")
        print("=" * 60)
        
        # Scan for video files
        video_files = self.scan_source_directory()
        if not video_files:
            print("❌ No video files found to process")
            return False
        
        self.stats["total_videos"] = len(video_files)
        
        # Process each video
        processed_results = []
        
        print(f"\n🔄 Processing {len(video_files)} videos...")
        for video_path in tqdm(video_files, desc="Processing videos"):
            result = self.process_single_video(video_path)
            processed_results.append(result)
        
        # Create speaker-wise data splits BEFORE augmentation
        splits = self.create_speaker_wise_splits(processed_results)

        # Apply augmentation to training set only
        augmented_train_data = self.apply_augmentation_to_training_set(splits['train'])
        splits['train'] = augmented_train_data

        # Create manifest with all data (including augmented)
        all_processed_data = splits['train'] + splits['val'] + splits['test']
        manifest_path = self.create_manifest(all_processed_data)
        
        # Save splits to separate files
        for split_name, split_data in splits.items():
            split_path = self.output_dir / f"{split_name}_manifest.csv"
            with open(split_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['video_path', 'phrase', 'tensor_shape', 'filename'])
                for result in split_data:
                    if result:
                        writer.writerow([
                            result['processed_path'],
                            result['core_word'],  # This will be the 'phrase' column
                            str(result['tensor_shape']),
                            result['filename']
                        ])
        
        # Save statistics
        self.save_processing_stats()

        # Save debug frames for ROI validation
        self.save_debug_frames(splits)
        
        # Print final summary
        print("\n" + "=" * 60)
        print("📊 PROCESSING SUMMARY")
        print("=" * 60)
        print(f"Total videos found: {self.stats['total_videos']}")
        print(f"Successfully processed: {self.stats['processed_videos']}")
        print(f"Failed to process: {self.stats['failed_videos']}")
        print(f"Success rate: {self.stats['processed_videos']/self.stats['total_videos']*100:.1f}%")
        print("\nCore word distribution:")
        for word, count in self.stats['core_word_counts'].items():
            print(f"  {word}: {count} videos")
        
        if self.stats['processing_errors']:
            print(f"\n⚠️  {len(self.stats['processing_errors'])} processing errors occurred")
        
        print(f"\n✅ Dataset processing complete!")
        print(f"📁 Processed data saved to: {self.output_dir}")
        print(f"📄 Training manifest: {manifest_path}")
        
        return True


def main():
    """Main execution function"""
    processor = CoreWordDatasetProcessor()
    success = processor.process_dataset()
    
    if success:
        print("\n🎯 Next steps:")
        print("1. Review processing statistics and core word distribution")
        print("2. Create core word training configuration")
        print("3. Train frozen encoder model on 5 core words")
        print("4. Validate >95% accuracy before proceeding to Phase 2")
    else:
        print("\n❌ Dataset processing failed. Check errors above.")
    
    return success


if __name__ == "__main__":
    main()
