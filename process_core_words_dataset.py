#!/usr/bin/env python3
"""
Dataset processing pipeline for hybrid core-word ICU lipreading system
Processes raw webm videos from desktop folder into training-ready format

Core Words: pillow, help, glasses, phone, doctor (5 anchor words for >95% accuracy)
"""

import os
import sys
import csv
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
import torch
from tqdm import tqdm
import re

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.mouth_detector import MouthDetector

class CoreWordDatasetProcessor:
    """Process raw webm videos for core word classification training"""
    
    # Define the 5 core words for hybrid system
    CORE_WORDS = ["pillow", "help", "glasses", "phone", "doctor"]
    
    def __init__(self, 
                 source_dir: str = "/Users/<USER>/Desktop/top 5 dataset 30.8.25",
                 output_dir: str = "data/core_words",
                 processed_dir: str = "data/core_words/processed"):
        """
        Initialize dataset processor
        
        Args:
            source_dir: Path to raw webm videos
            output_dir: Output directory for processed dataset
            processed_dir: Directory for processed video files
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.processed_dir = Path(processed_dir)
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize video processor with core word settings
        self.video_processor = VideoProcessor(
            target_frames=16,  # Reduced for core words
            target_size=(96, 96),
            grayscale=True,
            use_mouth_detection=True,
            mouth_detection_fallback=True
        )
        
        # Initialize mouth detector
        try:
            self.mouth_detector = MouthDetector()
            print("✅ Mouth detector initialized successfully")
        except Exception as e:
            print(f"⚠️  Mouth detector initialization failed: {e}")
            print("Will use fallback center cropping")
            self.mouth_detector = None
        
        # Statistics tracking
        self.stats = {
            "total_videos": 0,
            "processed_videos": 0,
            "failed_videos": 0,
            "core_word_counts": {word: 0 for word in self.CORE_WORDS},
            "processing_errors": []
        }
    
    def extract_core_word_from_filename(self, filename: str) -> Optional[str]:
        """
        Extract core word from video filename
        
        Args:
            filename: Video filename
            
        Returns:
            Core word if found, None otherwise
        """
        filename_lower = filename.lower()
        
        # Check for each core word in filename
        for word in self.CORE_WORDS:
            if word in filename_lower:
                return word
        
        # Additional pattern matching for variations
        word_patterns = {
            "pillow": ["pillow", "cushion"],
            "help": ["help", "assist", "aid"],
            "glasses": ["glasses", "spectacles", "eyeglasses"],
            "phone": ["phone", "telephone", "call"],
            "doctor": ["doctor", "physician", "dr"]
        }
        
        for core_word, patterns in word_patterns.items():
            for pattern in patterns:
                if pattern in filename_lower:
                    return core_word
        
        return None
    
    def process_single_video(self, video_path: Path) -> Optional[Dict]:
        """
        Process a single video file
        
        Args:
            video_path: Path to video file
            
        Returns:
            Processing result dictionary or None if failed
        """
        try:
            # Extract core word from filename
            core_word = self.extract_core_word_from_filename(video_path.name)
            if not core_word:
                print(f"⚠️  No core word found in filename: {video_path.name}")
                return None
            
            # Process video through pipeline
            print(f"Processing {video_path.name} -> {core_word}")
            
            # Use video processor to extract and process frames
            processed_tensor = self.video_processor.process_video(video_path)
            
            # Generate output filename
            output_filename = f"{core_word}_{video_path.stem}_processed.pt"
            output_path = self.processed_dir / output_filename
            
            # Save processed tensor
            torch.save(processed_tensor, output_path)
            
            # Update statistics
            self.stats["core_word_counts"][core_word] += 1
            self.stats["processed_videos"] += 1
            
            # Return metadata
            return {
                "original_path": str(video_path),
                "processed_path": str(output_path),
                "core_word": core_word,
                "filename": output_filename,
                "tensor_shape": list(processed_tensor.shape),
                "processing_status": "success"
            }
            
        except Exception as e:
            error_msg = f"Failed to process {video_path.name}: {str(e)}"
            print(f"❌ {error_msg}")
            self.stats["processing_errors"].append(error_msg)
            self.stats["failed_videos"] += 1
            return None
    
    def scan_source_directory(self) -> List[Path]:
        """
        Scan source directory for video files
        
        Returns:
            List of video file paths
        """
        video_extensions = ['.webm', '.mp4', '.avi', '.mov']
        video_files = []
        
        if not self.source_dir.exists():
            print(f"❌ Source directory not found: {self.source_dir}")
            return []
        
        print(f"🔍 Scanning {self.source_dir} for video files...")
        
        for ext in video_extensions:
            files = list(self.source_dir.glob(f"*{ext}"))
            video_files.extend(files)
            print(f"Found {len(files)} {ext} files")
        
        print(f"📊 Total video files found: {len(video_files)}")
        return video_files
    
    def create_manifest(self, processed_results: List[Dict]) -> str:
        """
        Create training manifest CSV file
        
        Args:
            processed_results: List of processing results
            
        Returns:
            Path to created manifest file
        """
        manifest_path = self.output_dir / "manifest.csv"
        
        print(f"📝 Creating manifest file: {manifest_path}")
        
        with open(manifest_path, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow(['video_path', 'core_word', 'tensor_shape', 'filename'])
            
            # Write data rows
            for result in processed_results:
                if result:  # Skip failed processing results
                    writer.writerow([
                        result['processed_path'],
                        result['core_word'],
                        str(result['tensor_shape']),
                        result['filename']
                    ])
        
        print(f"✅ Manifest created with {len(processed_results)} entries")
        return str(manifest_path)
    
    def create_data_splits(self, processed_results: List[Dict]) -> Dict[str, List[Dict]]:
        """
        Create train/validation/test splits (speaker-wise to prevent leakage)
        
        Args:
            processed_results: List of processing results
            
        Returns:
            Dictionary with train/val/test splits
        """
        # For now, use simple random splits
        # In production, implement speaker-wise splitting
        
        import random
        random.seed(42)  # Reproducible splits
        
        # Filter successful results
        valid_results = [r for r in processed_results if r is not None]
        random.shuffle(valid_results)
        
        # Calculate split sizes
        total = len(valid_results)
        train_size = int(0.7 * total)
        val_size = int(0.2 * total)
        
        splits = {
            'train': valid_results[:train_size],
            'val': valid_results[train_size:train_size + val_size],
            'test': valid_results[train_size + val_size:]
        }
        
        print(f"📊 Data splits created:")
        for split_name, split_data in splits.items():
            print(f"  {split_name}: {len(split_data)} samples")
            
            # Count per core word
            word_counts = {}
            for item in split_data:
                word = item['core_word']
                word_counts[word] = word_counts.get(word, 0) + 1
            print(f"    Word distribution: {word_counts}")
        
        return splits
    
    def save_processing_stats(self):
        """Save processing statistics to JSON file"""
        stats_path = self.output_dir / "processing_stats.json"
        
        with open(stats_path, 'w') as f:
            json.dump(self.stats, f, indent=2)
        
        print(f"📊 Processing statistics saved to {stats_path}")
    
    def process_dataset(self) -> bool:
        """
        Main processing pipeline
        
        Returns:
            True if processing successful, False otherwise
        """
        print("🚀 Starting core word dataset processing...")
        print(f"Source: {self.source_dir}")
        print(f"Output: {self.output_dir}")
        print(f"Target core words: {', '.join(self.CORE_WORDS)}")
        print("=" * 60)
        
        # Scan for video files
        video_files = self.scan_source_directory()
        if not video_files:
            print("❌ No video files found to process")
            return False
        
        self.stats["total_videos"] = len(video_files)
        
        # Process each video
        processed_results = []
        
        print(f"\n🔄 Processing {len(video_files)} videos...")
        for video_path in tqdm(video_files, desc="Processing videos"):
            result = self.process_single_video(video_path)
            processed_results.append(result)
        
        # Create manifest
        manifest_path = self.create_manifest(processed_results)
        
        # Create data splits
        splits = self.create_data_splits(processed_results)
        
        # Save splits to separate files
        for split_name, split_data in splits.items():
            split_path = self.output_dir / f"{split_name}_manifest.csv"
            with open(split_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['video_path', 'core_word', 'tensor_shape', 'filename'])
                for result in split_data:
                    if result:
                        writer.writerow([
                            result['processed_path'],
                            result['core_word'],
                            str(result['tensor_shape']),
                            result['filename']
                        ])
        
        # Save statistics
        self.save_processing_stats()
        
        # Print final summary
        print("\n" + "=" * 60)
        print("📊 PROCESSING SUMMARY")
        print("=" * 60)
        print(f"Total videos found: {self.stats['total_videos']}")
        print(f"Successfully processed: {self.stats['processed_videos']}")
        print(f"Failed to process: {self.stats['failed_videos']}")
        print(f"Success rate: {self.stats['processed_videos']/self.stats['total_videos']*100:.1f}%")
        print("\nCore word distribution:")
        for word, count in self.stats['core_word_counts'].items():
            print(f"  {word}: {count} videos")
        
        if self.stats['processing_errors']:
            print(f"\n⚠️  {len(self.stats['processing_errors'])} processing errors occurred")
        
        print(f"\n✅ Dataset processing complete!")
        print(f"📁 Processed data saved to: {self.output_dir}")
        print(f"📄 Training manifest: {manifest_path}")
        
        return True


def main():
    """Main execution function"""
    processor = CoreWordDatasetProcessor()
    success = processor.process_dataset()
    
    if success:
        print("\n🎯 Next steps:")
        print("1. Review processing statistics and core word distribution")
        print("2. Create core word training configuration")
        print("3. Train frozen encoder model on 5 core words")
        print("4. Validate >95% accuracy before proceeding to Phase 2")
    else:
        print("\n❌ Dataset processing failed. Check errors above.")
    
    return success


if __name__ == "__main__":
    main()
