#!/bin/bash

# Start backend server for mobile testing
# This script ensures the server is accessible from mobile devices

echo "🚀 Starting backend server for mobile testing..."
echo "================================================"

# Check if virtual environment exists
if [ ! -d ".venv_vsr" ]; then
    echo "❌ Virtual environment .venv_vsr not found"
    echo "Please create it first with: python -m venv .venv_vsr"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source .venv_vsr/bin/activate

# Set environment variables for lightweight VSR
echo "⚙️  Setting environment variables..."
export VSR_IMPL=lightweight
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Check if trained model exists
if [ ! -f "artifacts/vsr_fasthead_v1/best.pt" ]; then
    echo "⚠️  Trained model not found at artifacts/vsr_fasthead_v1/best.pt"
    echo "Please run the training first:"
    echo "   python run_real_test.py"
    echo ""
    echo "Or run training manually:"
    echo "   python -m backend.lightweight_vsr.train \\"
    echo "     --manifest data/manifest.csv \\"
    echo "     --config configs/phrases26.yaml \\"
    echo "     --out_dir artifacts/vsr_fasthead_v1 \\"
    echo "     --epochs 8 --batch_size 32 --frames 16 --size 96 \\"
    echo "     --min_per_phrase 8 --max_per_phrase 40 \\"
    echo "     --freeze_encoder"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Get LAN IP for display
LAN_IP=$(python3 -c "
import socket
try:
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    s.connect(('*******', 80))
    ip = s.getsockname()[0]
    s.close()
    print(ip)
except:
    print('unknown')
")

echo "🌐 Server will be accessible at: http://${LAN_IP}:8000"
echo "📱 Make sure your mobile device is on the same WiFi network"
echo ""
echo "🔧 Starting uvicorn server..."
echo "   (Press Ctrl+C to stop)"
echo ""

# Start the server with host 0.0.0.0 to accept connections from any IP
uvicorn backend.api.app:app --host 0.0.0.0 --port 8000 --reload
